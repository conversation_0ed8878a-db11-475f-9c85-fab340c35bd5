import logging
import os
import sys
import traceback
from logging.handlers import RotatingFileHandler
from datetime import datetime

class Logger:
    """
    A configurable logger for use throughout the project.
    Supports console and file logging with different log levels.
    """
    
    # Log levels dictionary for easy reference
    LOG_LEVELS = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    def __init__(self, name=None, log_level='INFO', log_to_file=True, log_dir='logs'):
        """
        Initialize the logger.
        
        Args:
            name (str, optional): Logger name. Defaults to the module name.
            log_level (str, optional): Logging level. Defaults to 'INFO'.
            log_to_file (bool, optional): Whether to log to file. Defaults to True.
            log_dir (str, optional): Directory for log files. Defaults to 'logs'.
        """
        # Use the module name if no name is provided
        self.name = name if name else __name__
        
        # Create logger
        self.logger = logging.getLogger(self.name)
        
        # Set log level
        self.set_log_level(log_level)
        
        # Clear existing handlers to avoid duplicates
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Create console handler
        self._setup_console_handler()
        
        # Create file handler if requested
        if log_to_file:
            self._setup_file_handler(log_dir)
    
    def _setup_console_handler(self):
        """Set up logging to console."""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(self._get_formatter())
        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self, log_dir):
        """Set up logging to file with rotation."""
        try:
            # Create log directory if it doesn't exist
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # Create a log file with timestamp
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = os.path.join(log_dir, f"{self.name}_{timestamp}.log")
            
            # Set up rotating file handler (10MB max size, keep 5 backup files)
            file_handler = RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
            file_handler.setFormatter(self._get_formatter())
            self.logger.addHandler(file_handler)
        except Exception as e:
            # If file logging fails, log to console only
            self.logger.warning(f"Failed to set up file logging: {str(e)}")
    
    def _get_formatter(self):
        """Return a standard log formatter."""
        return logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def set_log_level(self, level):
        """
        Set the logging level.
        
        Args:
            level (str): Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        if level.upper() in self.LOG_LEVELS:
            self.logger.setLevel(self.LOG_LEVELS[level.upper()])
        else:
            # Default to INFO if invalid level
            self.logger.setLevel(logging.INFO)
            self.logger.warning(f"Invalid log level: {level}. Using INFO instead.")
    
    def debug(self, message):
        """Log a debug message."""
        self.logger.debug(message)
    
    def info(self, message):
        """Log an info message."""
        self.logger.info(message)
    
    def warning(self, message):
        """Log a warning message."""
        self.logger.warning(message)
    
    def error(self, message, exc_info=False):
        """
        Log an error message.
        
        Args:
            message (str): Error message
            exc_info (bool, optional): Whether to include exception info. Defaults to False.
        """
        self.logger.error(message, exc_info=exc_info)
    
    def critical(self, message, exc_info=True):
        """
        Log a critical message.
        
        Args:
            message (str): Critical error message
            exc_info (bool, optional): Whether to include exception info. Defaults to True.
        """
        self.logger.critical(message, exc_info=exc_info)
    
    def exception(self, message):
        """
        Log an exception message with traceback.
        
        Args:
            message (str): Exception message
        """
        self.logger.exception(message)


# Create a default logger instance for easy import
default_logger = Logger(name="app")


def get_logger(name=None, log_level=None, log_to_file=True, log_dir='logs'):
    """
    Get a configured logger instance.
    
    Args:
        name (str, optional): Logger name. Defaults to the module name.
        log_level (str, optional): Logging level. Defaults to 'INFO'.
        log_to_file (bool, optional): Whether to log to file. Defaults to True.
        log_dir (str, optional): Directory for log files. Defaults to 'logs'.
    
    Returns:
        Logger: Configured logger instance
    """
    # Use environment variable for log level if not specified
    if log_level is None:
        log_level = os.environ.get('LOG_LEVEL', 'INFO')
    
    return Logger(name=name, log_level=log_level, log_to_file=log_to_file, log_dir=log_dir)

__all__ = [
    "Logger",
    "get_logger",
    "default_logger"
]
