---
description: 
globs: 
alwaysApply: true
---

# Migration to crew agents

## Trying to achieve

    To dynamically create crew agents, task and crews based on the specifications document.

## reference links

1. Read documentations on links "https://docs.crewai.com/guides/agents/crafting-effective-agents", "https://docs.crewai.com/how-to/customizing-agents" and "https://docs.crewai.com/concepts/agents" for agent creations.

2. Read documentaion on links "https://docs.crewai.com/concepts/tools" and "https://docs.crewai.com/how-to/create-custom-tools" for tools creation.

3. Read documentation on links "https://docs.crewai.com/concepts/tasks" for tasks creation.

4. Read documentation on links "https://docs.crewai.com/concepts/crews" for crew creation.

5. Read documentation on links "https://docs.crewai.com/concepts/flows" for flows.

# RULES TO BE FOLLOWED ABSOLUTELY:
1. MAKE SURE THE CREATED AGENTS ARE SYNTAXICALLY CORRECT BASED ON DOCUMENTATION.