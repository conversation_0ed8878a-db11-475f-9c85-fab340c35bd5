{
    'additional_properties': {},
    'id': '/subscriptions/0c4ce32a-8bbc-4425-8ecc-49ca1efe01f5/resourceGroups/bincha-test-1_group/providers/Microsoft.Web/sites/AFtestapiroute', 
    'name': 'AFtestapiroute', 
    'kind': 'functionapp,linux',
    'location': 'West US', 
    'type': 'Microsoft.Web/sites', 
    'tags': None, 
    'identity': None, 
    'extended_location': None, 
    'state': 'Running', 
    'host_names': ['aftestapiroute.azurewebsites.net'], 
    'repository_site_name': 'AFtestapiroute', 
    'usage_state': 'Normal', 
    'enabled': True, 
    'enabled_host_names': ['aftestapiroute.azurewebsites.net', 'aftestapiroute.scm.azurewebsites.net'], 
    'availability_state': 'Normal', 
    'host_name_ssl_states': [<azure.mgmt.web.v2024_04_01.models._models_py3.HostNameSslState object at 0x107c39220>, <azure.mgmt.web.v2024_04_01.models._models_py3.HostNameSslState object at 0x107c39250>], 
    'server_farm_id': '/subscriptions/0c4ce32a-8bbc-4425-8ecc-49ca1efe01f5/resourceGroups/bincha-test-1_group/providers/Microsoft.Web/serverfarms/binchatest1group', 
    'reserved': True, 
    'is_xenon': False, 
    'hyper_v': False, 
    'last_modified_time_utc': datetime.datetime(2025, 5, 6, 8, 11, 31, 693333), 
    'dns_configuration': <azure.mgmt.web.v2024_04_01.models._models_py3.SiteDnsConfig object at 0x107c391f0>, 
    'vnet_route_all_enabled': False, 
    'vnet_image_pull_enabled': False, 
    'vnet_content_share_enabled': False, 
    'vnet_backup_restore_enabled': False, 
    'site_config': <azure.mgmt.web.v2024_04_01.models._models_py3.SiteConfig object at 0x107c392b0>, 
    'function_app_config': None, 
    'dapr_config': None, 
    'workload_profile_name': None, 
    'resource_config': None, 
    'traffic_manager_host_names': None, 
    'scm_site_also_stopped': False, 
    'target_swap_slot': None, 
    'hosting_environment_profile': None, 
    'client_affinity_enabled': False, 
    'client_cert_enabled': False, 
    'client_cert_mode': 'Required', 
    'client_cert_exclusion_paths': None, 
    'ip_mode': 'IPv4', 
    'end_to_end_encryption_enabled': False, 
    'host_names_disabled': False, 
    'custom_domain_verification_id': '1DDBF5C9ABB085C6594162C7C937C5FB206114E833AC9493B33D8E0E71BD5010', 
    'outbound_ip_addresses': '************,**************,**************,*************,************', 
    'possible_outbound_ip_addresses': '************,**************,**************,*************,*************,************,************,************,************,**************,**************,**************,**************,**************,**************,**************,*************,*************,************', 
    'container_size': 0, 
    'daily_memory_time_quota': 0, 
    'suspended_till': None, 
    'max_number_of_workers': None, 
    'cloning_info': None, 
    'resource_group': 'bincha-test-1_group', 
    'is_default_container': None, 
    'default_host_name': 'aftestapiroute.azurewebsites.net', 
    'slot_swap_status': None, 
    'https_only': False, 
    'redundancy_mode': 'None', 
    'in_progress_operation_id': None, 
    'public_network_access': None, 
    'storage_account_required': False, 
    'key_vault_reference_identity': 'SystemAssigned', 
    'auto_generated_domain_name_label_scope': None, 
    'virtual_network_subnet_id': None, 
    'managed_environment_id': None, 
    'sku': 'Dynamic'
}