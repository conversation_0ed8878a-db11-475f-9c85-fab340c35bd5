from fastapi import APIRouter, Request, HTTPException, BackgroundTasks
from meta_agent.generators.azure_functions import create_function_app, deploy_to_azure
import os
from logger import get_logger
from utils import format_success_response, format_failure_response, format_error_response, format_unauthorized_response, format_exception_response
from pydantic import BaseModel
from typing import Optional, Dict, Any
import re
import tempfile
import json
from meta_agent.generators.azure_functions.template_manager import AzureFunctionTemplateManager
from meta_agent.generators.azure_functions.crew_to_azure_function_generator import generate_azure_function, generate_azure_multi_function, add_function_script, generate_template_crew_function
from utils.azure_function_utils import get_function_urls_from_azure, construct_function_urls_manually
from utils.file_utils import backup_function_app_files, rollback_to_previous_backup
from meta_agent.utils import run_in_process
from meta_agent.utils.file_utils import get_project_root
import requests

logger = get_logger(__name__)   


router = APIRouter()

#data model
class CreateFunctionAppRequest(BaseModel):
    subscription_id: Optional[str] = None
    resource_group_name: Optional[str] = None
    location: Optional[str] = None
    storage_account_name: Optional[str] = None
    function_app_name: Optional[str] = None
    app_service_plan_name: Optional[str] = None

class DeployFunctionRequest(BaseModel):
    function_app_name: Optional[str] = None
    resource_group: Optional[str] = None
    output_dir: str

class GenerateAndDeployCrewFunctionsRequest(BaseModel):
    team_id: Optional[str] = None
    function_app_name: Optional[str] = None
    resource_group: Optional[str] = None
    store_name: str
    crew_spec: Optional[Dict[str, Any]] = None
    crew_spec_path: Optional[str] = None
    deploy: bool = True
    callback_url: Optional[str] = None

def sanitize_folder_name(name: str) -> str:
    # Lowercase, remove spaces and special characters
    return re.sub(r'[^a-z0-9_]', '', name.lower().replace(' ', '_'))

def get_or_create_temp_dir() -> str:
    temp_dir = '/tmp/crew_functions'
    os.makedirs(temp_dir, exist_ok=True)
    return temp_dir

def get_crew_spec(crew_spec: Optional[Dict[str, Any]], crew_spec_path: Optional[str]) -> Dict[str, Any]:
    if crew_spec:
        return crew_spec
    elif crew_spec_path:
        with open(crew_spec_path, 'r') as f:
            return json.load(f)
    else:
        raise ValueError('Either crew_spec or crew_spec_path must be provided')

def write_spec_to_temp(spec: Dict[str, Any], temp_dir: str, function_folder: str) -> str:
    spec_path = os.path.join(temp_dir, f'{function_folder}_spec.json')
    with open(spec_path, 'w') as f:
        json.dump(spec, f)
    return spec_path

def copy_template_to_folder(output_dir: str) -> bool:
    template_manager = AzureFunctionTemplateManager()
    return template_manager.create_function_from_template(output_dir)

def generate_crew_function_code(spec_path: str, output_dir: str):
    generate_azure_function(spec_path, output_dir)

def deploy_function_to_azure(output_dir: str, function_app_name: str, resource_group: str) -> bool:
    template_manager = AzureFunctionTemplateManager()
    return template_manager.deploy_to_azure(output_dir, function_app_name, resource_group)

# create a new azure functions app
@router.post("/functions-app",response_description="Creating a new azure functions app")
async def create_functions_app(request: CreateFunctionAppRequest):
    logger.info("Creating a new azure functions app - endpoint - /create-functions-app")
    try:
        subscription_id = request.subscription_id or os.getenv("AZURE_SUBSCRIPTION_ID")
        resource_group_name = request.resource_group_name or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        location = request.location or os.getenv("AZURE_LOCATION")
        storage_account_name = request.storage_account_name or os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
        function_app_name = request.function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
        app_service_plan_name = request.app_service_plan_name or os.getenv("AZURE_APP_SERVICE_PLAN_NAME")

        #create the function app
        function_app = create_function_app(
            subscription_id, resource_group_name, location,
            storage_account_name, function_app_name, app_service_plan_name
        )

        logger.info(f"Function app created: {function_app}")

        # get the function app url
        function_app_url = f"https://{function_app.default_host_name}"

        # response data 
        response_data = {
            "url": function_app_url,
            "app_name": function_app_name,
            "resource_group": resource_group_name
        }
        #return the function app
        return format_success_response(message="Function app created successfully", data=response_data)
    except Exception as e:
        logger.error(f"Error creating function app: {e}")
        return format_failure_response(message="Error creating function app", error=str(e))
 
# deploy a function to the function app
@router.post("/functions",response_description="Deploying a function to the function app")
async def deploy_function(request: DeployFunctionRequest):
    logger.info("Deploying a function to the function app - endpoint - /deploy-function")
    try:
        # get the function app name
        function_app_name = request.function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
        resource_group = request.resource_group or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        # get the output directory
        output_dir = request.output_dir
        # deploy the function
        deploy_to_azure(output_dir, function_app_name, resource_group)

        # response data 
        response_data = {
            "message": "Function deployed successfully"
        }
        return format_success_response(message="Function deployed successfully", data=response_data)
    except Exception as e:
        logger.error(f"Error deploying function: {e}")
        return format_failure_response(message="Error deploying function", error=str(e))

# generate crew functions and then deploy them to the function app
@router.post("/crew-function",response_description="Generating and deploying crew functions to the function app")
async def generate_and_deploy_crew_functions(request: GenerateAndDeployCrewFunctionsRequest):
    logger.info("Generating and deploying crew functions to the function app - endpoint - /generate-and-deploy-crew-functions")
    try:
        function_app_name = request.function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
        resource_group = request.resource_group or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        store_name = request.store_name
        deploy = request.deploy
        # Sanitize folder name
        function_folder = sanitize_folder_name(f"{function_app_name}_{store_name}")
        # The output_dir is always the root of the function app folder
        output_dir = os.path.join(get_project_root(), "crew_scripts", function_folder)
        # Copy template
        if not copy_template_to_folder(output_dir):
            raise Exception("Failed to copy Azure Functions template.")
        # Handle crew spec
        temp_dir = get_or_create_temp_dir()
        crew_spec = get_crew_spec(request.crew_spec, request.crew_spec_path)
        spec_path = write_spec_to_temp(crew_spec, temp_dir, function_folder)
        # Generate crew function code
        generate_crew_function_code(spec_path, output_dir)
        # Backup before deployment
        backup_root = os.path.join(get_project_root(), "crew_scripts", f"{function_folder}_backups")
        try:
            backup_path = backup_function_app_files(output_dir, backup_root)
            logger.info(f"Backup created at {backup_path}")
        except Exception as backup_exc:
            logger.error(f"Backup failed: {backup_exc}. Attempting rollback.")
            rollbacked = rollback_to_previous_backup(backup_root, output_dir)
            msg = f"Backup failed and rollbacked to previous version at {rollbacked}" if rollbacked else "Backup failed and no previous version to rollback."
            return format_failure_response(message=msg, error=str(backup_exc))
        # Deploy if requested
        deployed = False
        function_urls = {}
        if deploy:
            # Always deploy the root output_dir
            deployed = deploy_function_to_azure(output_dir, function_app_name, resource_group)
            if not deployed:
                raise Exception("Failed to deploy function to Azure.")
            # Try to fetch function URLs using Azure SDK, fallback to manual
            subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
            try:
                function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
            except Exception as e:
                logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                function_urls = construct_function_urls_manually(function_app_name, output_dir)
        response_data = {
            "message": "Crew functions generated{} successfully".format(" and deployed" if deploy else ""),
            "output_dir": output_dir,
            "backup_path": backup_path,
            "deployed_status": deploy,
            "deployed": deployed,
            "function_urls": function_urls
        }
        return format_success_response(message=response_data["message"], data=response_data)
    except Exception as e:
        logger.error(f"Error generating and deploying crew functions: {e}")
        return format_failure_response(message="Error generating and deploying crew functions", error=str(e))

# generate multi-crew functions and then deploy them to the function app
@router.post("/multi-crew-function", response_description="Generating and deploying multi-function Azure Functions app")
async def generate_and_deploy_multi_crew_functions(request: GenerateAndDeployCrewFunctionsRequest):
    logger.info("Generating and deploying multi-function Azure Functions app - endpoint - /multi-crew-function")
    try:
        function_app_name = request.function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
        resource_group = request.resource_group or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        store_name = request.store_name
        deploy = request.deploy
        function_folder = sanitize_folder_name(f"{function_app_name}_{store_name}")
        output_dir = os.path.join(get_project_root(), "crew_scripts", function_folder)
        backup_root = os.path.join(get_project_root(), "crew_scripts", "backups", function_folder)
        temp_dir = get_or_create_temp_dir()
        crew_spec = get_crew_spec(request.crew_spec, request.crew_spec_path)
        class_name = crew_spec['name'].replace(' ', '')
        spec_path = write_spec_to_temp(crew_spec, temp_dir, function_folder)
        deployed = False
        function_urls = {}
        backup_path = None
        # CASE 1: Directory exists (add new function)
        if os.path.exists(output_dir):
            logger.info(f"Function app directory {output_dir} exists. Adding or updating function {class_name}.")
            func_folder_path = os.path.join(output_dir, class_name)
            # If the function folder exists, remove it to allow overwrite
            if os.path.exists(func_folder_path):
                logger.info(f"Function folder {func_folder_path} already exists. Overwriting with new spec.")
                import shutil
                try:
                    shutil.rmtree(func_folder_path)
                except Exception as e:
                    logger.error(f"Failed to remove existing function folder {func_folder_path}: {e}")
                    return format_failure_response(message=f"Failed to remove existing function folder {class_name}.", error=str(e))
            try:
                # Add the function script (now always adds, since old one is removed if present)
                add_function_script(function_folder, spec_path)
            except Exception as e:
                logger.error(f"Error adding function script: {e}")
                return format_failure_response(message="Error adding function script", error=str(e))
            # Backup before deployment
            try:
                backup_path = backup_function_app_files(output_dir, backup_root)
                logger.info(f"Backup created at {backup_path}")
            except Exception as backup_exc:
                logger.warning(f"Backup failed: {backup_exc}. Proceeding with deployment.")
            # Deploy the entire function app directory
            if deploy:
                deployed = deploy_function_to_azure(output_dir, function_app_name, resource_group)
                if not deployed:
                    logger.error("Deployment failed. Rolling back to previous backup.")
                    rollbacked = rollback_to_previous_backup(backup_root, output_dir)
                    msg = f"Deployment failed and rollbacked to previous version at {rollbacked}" if rollbacked else "Deployment failed and no previous version to rollback."
                    rollback_redeploy_status = False
                    if rollbacked:
                        # Try to redeploy the restored backup
                        logger.info("Attempting to redeploy the restored backup after rollback.")
                        redeploy_success = deploy_function_to_azure(output_dir, function_app_name, resource_group)
                        if redeploy_success:
                            logger.info("Rollback redeployment succeeded.")
                            rollback_redeploy_status = True
                        else:
                            logger.error("Rollback redeployment failed.")
                            return format_failure_response(
                                message=f"{msg}. Rollback redeployment to Azure also failed.",
                                error="Deployment failed and rollback redeployment failed.",
                                data={
                                    "rollback_redeploy_status": False,
                                    "function_app_name": function_app_name
                                }
                            )
                    return format_failure_response(
                        message=msg,
                        error="Deployment failed.",
                        data={
                            "rollback_redeploy_status": rollback_redeploy_status,
                            "function_app_name": function_app_name
                        }
                    )
                # Fetch function URLs
                subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
                try:
                    function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
                except Exception as e:
                    logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                    function_urls = construct_function_urls_manually(function_app_name, output_dir)
        # CASE 2: Directory does not exist (create new app)
        else:
            logger.info(f"Function app directory {output_dir} does not exist. Creating new app.")
            import tempfile, shutil
            with tempfile.TemporaryDirectory() as temp_app_dir:
                # Copy template
                template_dir = os.path.join(get_project_root(), "template", "multifunction-azure")
                try:
                    shutil.copytree(template_dir, temp_app_dir, dirs_exist_ok=True)
                except Exception as e:
                    logger.error(f"Failed to copy template: {e}")
                    return format_failure_response(message="Failed to copy Azure Functions template.", error=str(e))
                # Add the function script
                try:
                    # Use add_function_script logic, but target temp_app_dir
                    from meta_agent.generators.azure_functions.crew_to_azure_function_generator import AzureMultiFunctionGeneratorForTemplates, _write_function_json
                    generator = AzureMultiFunctionGeneratorForTemplates(spec_path, temp_app_dir)
                    class_name = crew_spec['name'].replace(' ', '')
                    func_dir = os.path.join(temp_app_dir, class_name)
                    os.makedirs(func_dir, exist_ok=True)
                    init_code = generator._generate_function_init(class_name, class_name.lower())
                    with open(os.path.join(func_dir, '__init__.py'), 'w') as f:
                        f.write(init_code)
                    _write_function_json(func_dir, class_name.lower(), ['post'])
                except Exception as e:
                    logger.error(f"Error generating function code: {e}")
                    return format_failure_response(message="Error generating function code", error=str(e))
                # Deploy from temp_app_dir
                if deploy:
                    deployed = deploy_function_to_azure(temp_app_dir, function_app_name, resource_group)
                    if not deployed:
                        logger.error("Deployment failed from temp dir. Not creating app directory.")
                        return format_failure_response(message="Deployment failed from temp dir.", error="Deployment failed.")
                    # Move to crew_scripts
                    try:
                        shutil.move(temp_app_dir, output_dir)
                        logger.info(f"Moved deployed app to {output_dir}")
                    except Exception as e:
                        logger.error(f"Failed to move deployed app to crew_scripts: {e}")
                        return format_failure_response(message="Failed to move deployed app to crew_scripts", error=str(e))
                    # Backup after successful deployment
                    try:
                        backup_path = backup_function_app_files(output_dir, backup_root)
                        logger.info(f"Backup created at {backup_path}")
                    except Exception as backup_exc:
                        logger.warning(f"Backup failed after deployment: {backup_exc}.")
                    # Fetch function URLs
                    subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
                    try:
                        function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
                    except Exception as e:
                        logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                        function_urls = construct_function_urls_manually(function_app_name, output_dir)
        # Prepare refined response
        function_url = None
        endpoint = None
        if function_urls and class_name in function_urls:
            function_url = function_urls[class_name]
            endpoint = f"/api/{class_name}"
            function_name = class_name
        else:
            # fallback: construct manually if not found
            function_url = f"https://{function_app_name}.azurewebsites.net/api/{class_name}"
            endpoint = f"/api/{class_name}"
            function_name = class_name
        response_data = {
            "message": "Multi-crew function generated{} successfully".format(" and deployed" if deploy else ""),
            "deployed_status": deploy,
            "function_app_name": function_app_name,
            "url": function_url,
            "endpoint": endpoint,
            "function_name": function_name
        }
        return format_success_response(message=response_data["message"], data=response_data)
    except Exception as e:
        logger.error(f"Error generating and deploying multi-crew functions: {e}")
        return format_failure_response(message="Error generating and deploying multi-crew functions", error=str(e))

from pydantic import BaseModel
from typing import Optional

class AddFunctionScriptRequest(BaseModel):
    directory_name: str
    crew_spec: Optional[Dict[str, Any]] = None
    crew_spec_path: Optional[str] = None
    function_app_name: Optional[str] = None
    resource_group: Optional[str] = None
    deploy: bool = True

# add a new function script to an existing crew_scripts directory and deploy
@router.post("/add-function-script", response_description="Add a new function script to an existing crew_scripts directory and deploy")
async def add_function_script_and_deploy(request: AddFunctionScriptRequest):
    logger.info("Adding a new function script and deploying - endpoint - /add-function-script")
    try:
        directory_name = request.directory_name
        function_app_name = request.function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
        resource_group = request.resource_group or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        deploy = request.deploy
        # Handle crew spec
        temp_dir = get_or_create_temp_dir()
        crew_spec = get_crew_spec(request.crew_spec, request.crew_spec_path)
        spec_path = write_spec_to_temp(crew_spec, temp_dir, directory_name)
        # Add the function script
        add_function_script(directory_name, spec_path)
        # Backup before deployment
        output_dir = os.path.join(get_project_root(), "crew_scripts", directory_name)
        backup_root = os.path.join(get_project_root(), "crew_scripts", f"{directory_name}_backups")
        try:
            backup_path = backup_function_app_files(output_dir, backup_root)
            logger.info(f"Backup created at {backup_path}")
        except Exception as backup_exc:
            logger.error(f"Backup failed: {backup_exc}. Attempting rollback.")
            rollbacked = rollback_to_previous_backup(backup_root, output_dir)
            msg = f"Backup failed and rollbacked to previous version at {rollbacked}" if rollbacked else "Backup failed and no previous version to rollback."
            return format_failure_response(message=msg, error=str(backup_exc))
        # Deploy if requested
        deployed = False
        function_urls = {}
        if deploy:
            # Always deploy the root output_dir (function app folder)
            deployed = deploy_function_to_azure(output_dir, function_app_name, resource_group)
            if not deployed:
                raise Exception("Failed to deploy function to Azure.")
            # Try to fetch function URLs using Azure SDK, fallback to manual
            subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
            try:
                function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
            except Exception as e:
                logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                function_urls = construct_function_urls_manually(function_app_name, output_dir)
        response_data = {
            "message": "Function script added{} successfully".format(" and deployed" if deploy else ""),
            "output_dir": output_dir,
            "backup_path": backup_path,
            "deployed_status": deploy,
            "deployed": deployed,
            "function_urls": function_urls
        }
        return format_success_response(message=response_data["message"], data=response_data)
    except Exception as e:
        logger.error(f"Error adding function script and deploying: {e}")
        return format_failure_response(message="Error adding function script and deploying", error=str(e))

# Utility function to send deployment status to callback URL

def send_deployment_status_callback(callback_url: str, fallback_url: Optional[str], payload: dict, success: bool = True):
    logger = get_logger(__name__)
    # Use the response formatter to match the endpoint's response structure
    if success:
        response_obj = format_success_response(message=payload.get('message', ''), data=payload, status_code=200)
    else:
        response_obj = format_failure_response(message=payload.get('message', ''), error=payload.get('error', ''), status_code=400)
    # Extract the dict to send as JSON
    if hasattr(response_obj, 'body'):
        import json
        response_dict = json.loads(response_obj.body)
    else:
        response_dict = response_obj
    try:
        logger.info(f"Sending deployment status callback to {callback_url} with payload: {response_dict}")
        resp = requests.post(callback_url, json=response_dict, timeout=10)
        logger.info(f"Callback response status: {resp.status_code}, body: {resp.text}")
        if resp.status_code >= 200 and resp.status_code < 300:
            logger.info(f"Callback to {callback_url} succeeded.")
            return True
        else:
            logger.warning(f"Callback to {callback_url} failed with status {resp.status_code}. Trying fallback if available.")
            if fallback_url:
                try:
                    logger.info(f"Sending fallback callback to {fallback_url} with payload: {response_dict}")
                    fallback_resp = requests.post(fallback_url, json=response_dict, timeout=10)
                    logger.info(f"Fallback callback response status: {fallback_resp.status_code}, body: {fallback_resp.text}")
                except Exception as fallback_exc:
                    logger.error(f"Exception during fallback callback to {fallback_url}: {fallback_exc}")
            return False
    except Exception as e:
        logger.error(f"Exception during callback to {callback_url}: {e}")
        if fallback_url:
            try:
                logger.info(f"Sending fallback callback to {fallback_url} with payload: {response_dict}")
                fallback_resp = requests.post(fallback_url, json=response_dict, timeout=10)
                logger.info(f"Fallback callback response status: {fallback_resp.status_code}, body: {fallback_resp.text}")
            except Exception as fallback_exc:
                logger.error(f"Exception during fallback callback to {fallback_url}: {fallback_exc}")
        return False

def deployment_worker_function(
    request_dict,
    callback_url,
    fallback_callback_url
):
    import shutil
    from meta_agent.generators.azure_functions.crew_to_azure_function_generator import (
        AzureMultiFunctionGeneratorForTemplates, _write_function_json
    )
    from utils import format_success_response, format_failure_response
    logger = get_logger(__name__)
    try:
        function_app_name = request_dict.get('function_app_name') or os.getenv("AZURE_FUNCTION_APP_NAME")
        resource_group = request_dict.get('resource_group') or os.getenv("AZURE_RESOURCE_GROUP_NAME")
        store_name = request_dict.get('store_name')
        deploy = request_dict.get('deploy', True)
        team_id = request_dict.get('team_id')
        crew_spec = request_dict.get('crew_spec')
        crew_spec_path = request_dict.get('crew_spec_path')
        get_crew_spec = globals()['get_crew_spec']
        write_spec_to_temp = globals()['write_spec_to_temp']
        get_project_root = globals()['get_project_root']
        backup_function_app_files = globals()['backup_function_app_files']
        rollback_to_previous_backup = globals()['rollback_to_previous_backup']
        get_function_urls_from_azure = globals()['get_function_urls_from_azure']
        construct_function_urls_manually = globals()['construct_function_urls_manually']
        sanitize_folder_name = globals()['sanitize_folder_name']
        run_in_process = globals()['run_in_process']
        # Prepare paths and spec
        function_folder = sanitize_folder_name(f"{function_app_name}_{store_name}")
        output_dir = os.path.join(get_project_root(), "crew_scripts", function_folder)
        backup_root = os.path.join(get_project_root(), "crew_scripts", "backups", function_folder)
        temp_dir = get_or_create_temp_dir()
        crew_spec = get_crew_spec(crew_spec, crew_spec_path)
        class_name = crew_spec['name'].replace(' ', '')
        spec_path = write_spec_to_temp(crew_spec, temp_dir, function_folder)
        deployed = False
        function_urls = {}
        backup_path = None
        # CASE 1: Directory exists (add new function)
        if os.path.exists(output_dir):
            logger.info(f"Function app directory {output_dir} exists. Adding or updating function {class_name}.")
            func_folder_path = os.path.join(output_dir, class_name)
            if os.path.exists(func_folder_path):
                logger.info(f"Function folder {func_folder_path} already exists. Overwriting with new spec.")
                try:
                    shutil.rmtree(func_folder_path)
                except Exception as e:
                    logger.error(f"Failed to remove existing function folder {func_folder_path}: {e}")
                    payload = {
                        "status": "failure",
                        "message": f"Failed to remove existing function folder {class_name}.",
                        "error": str(e),
                        "team_id": team_id
                    }
                    send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                    return
            try:
                generator = AzureMultiFunctionGeneratorForTemplates(spec_path, func_folder_path)
                os.makedirs(func_folder_path, exist_ok=True)
                init_code = generator._generate_function_init(class_name, class_name.lower())
                with open(os.path.join(func_folder_path, '__init__.py'), 'w') as f:
                    f.write(init_code)
                _write_function_json(func_folder_path, class_name.lower(), ['post'])
            except Exception as e:
                logger.error(f"Error generating template crew function: {e}")
                payload = {
                    "status": "failure",
                    "message": "Error generating template crew function",
                    "error": str(e),
                    "team_id": team_id
                }
                send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                return
            try:
                backup_path = backup_function_app_files(output_dir, backup_root)
                logger.info(f"Backup created at {backup_path}")
            except Exception as backup_exc:
                logger.warning(f"Backup failed: {backup_exc}. Proceeding with deployment.")
            if deploy:
                try:
                    deployed = deploy_function_to_azure(output_dir, function_app_name, resource_group)
                except Exception as e:
                    logger.error("Deployment failed. Rolling back to previous backup.")
                    rollbacked = rollback_to_previous_backup(backup_root, output_dir)
                    msg = f"Deployment failed and rollbacked to previous version at {rollbacked}" if rollbacked else "Deployment failed and no previous version to rollback."
                    payload = {
                        "status": "failure",
                        "message": msg,
                        "error": "Deployment failed.",
                        "team_id": team_id
                    }
                    send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                    return
                subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
                try:
                    function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
                except Exception as e:
                    logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                    function_urls = construct_function_urls_manually(function_app_name, output_dir)
        else:
            logger.info(f"Function app directory {output_dir} does not exist. Creating new app.")
            import tempfile
            with tempfile.TemporaryDirectory() as temp_app_dir:
                template_dir = os.path.join(get_project_root(), "template", "multifunction-azure")
                try:
                    shutil.copytree(template_dir, temp_app_dir, dirs_exist_ok=True)
                except Exception as e:
                    logger.error(f"Failed to copy template: {e}")
                    payload = {
                        "status": "failure",
                        "message": "Failed to copy Azure Functions template.",
                        "error": str(e),
                        "team_id": team_id
                    }
                    send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                    return
                try:
                    generator = AzureMultiFunctionGeneratorForTemplates(spec_path, temp_app_dir)
                    func_dir = os.path.join(temp_app_dir, class_name)
                    os.makedirs(func_dir, exist_ok=True)
                    init_code = generator._generate_function_init(class_name, class_name.lower())
                    with open(os.path.join(func_dir, '__init__.py'), 'w') as f:
                        f.write(init_code)
                    _write_function_json(func_dir, class_name.lower(), ['post'])
                except Exception as e:
                    logger.error(f"Error generating template crew function: {e}")
                    payload = {
                        "status": "failure",
                        "message": "Error generating template crew function",
                        "error": str(e),
                        "team_id": team_id
                    }
                    send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                    return
                if deploy:
                    try:
                        deployed = deploy_function_to_azure(temp_app_dir, function_app_name, resource_group)
                    except Exception as e:
                        logger.error("Deployment failed from temp dir. Not creating app directory.")
                        payload = {
                            "status": "failure",
                            "message": "Deployment failed from temp dir.",
                            "error": "Deployment failed.",
                            "team_id": team_id
                        }
                        send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                        return
                    try:
                        shutil.move(temp_app_dir, output_dir)
                        logger.info(f"Moved deployed app to {output_dir}")
                    except Exception as e:
                        logger.error(f"Failed to move deployed app to crew_scripts: {e}")
                        payload = {
                            "status": "failure",
                            "message": "Failed to move deployed app to crew_scripts",
                            "error": str(e),
                            "team_id": team_id
                        }
                        send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)
                        return
                    try:
                        backup_path = backup_function_app_files(output_dir, backup_root)
                        logger.info(f"Backup created at {backup_path}")
                    except Exception as backup_exc:
                        logger.warning(f"Backup failed after deployment: {backup_exc}.")
                    subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
                    try:
                        function_urls = get_function_urls_from_azure(function_app_name, resource_group, subscription_id)
                    except Exception as e:
                        logger.warning(f"Azure SDK function URL fetch failed: {e}, falling back to manual construction.")
                        function_urls = construct_function_urls_manually(function_app_name, output_dir)
        function_url = None
        endpoint = None
        if function_urls and class_name in function_urls:
            function_url = function_urls[class_name]
            endpoint = f"/api/{class_name}"
            function_name = class_name
        else:
            function_url = f"https://{function_app_name}.azurewebsites.net/api/{class_name}"
            endpoint = f"/api/{class_name}"
            function_name = class_name
        response_data = {
            "message": "Template crew function generated{} successfully".format(" and deployed" if deploy else ""),
            "team_id": team_id,
            "deployed_status": deploy,
            "function_app_name": function_app_name,
            "url": function_url,
            "endpoint": endpoint,
            "function_name": function_name,
            "backup_path": backup_path
        }
        send_deployment_status_callback(callback_url, fallback_callback_url, response_data, True)
    except Exception as e:
        logger.error(f"Error generating and deploying template crew function: {e}")
        payload = {
            "message": "Error generating and deploying template crew function",
            "error": str(e),
            "team_id": request_dict.get('team_id')
        }
        send_deployment_status_callback(callback_url, fallback_callback_url, payload, False)

@router.post("/template-crew-function", response_description="Generating and deploying template-based crew function")
async def generate_and_deploy_template_crew_functions(request: GenerateAndDeployCrewFunctionsRequest, background_tasks: BackgroundTasks):
    logger.info("Generating and deploying template crew function - endpoint - /template-crew-function")
    callback_url = request.callback_url or os.getenv("PROACTIVE_SERVER_CALLBACK_URL_FOR_AZURE_FUNCTION_DEPLOYMENT")
    fallback_callback_url = os.getenv("PROACTIVE_SERVER_FALLBACK_CALLBACK_URL_FOR_AZURE_FUNCTION_DEPLOYMENT")
    # Schedule the deployment in the background and return immediately
    background_tasks.add_task(
        deployment_worker_function,
        request.dict(),
        callback_url,
        fallback_callback_url
    )
    return format_success_response(message="Deployment started. You will receive the result at the callback URL.", data={"status": "started"})
