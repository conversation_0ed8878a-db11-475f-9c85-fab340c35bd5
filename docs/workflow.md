# Meta-Agent Workflow Documentation

## Overview
Meta-Agent is a system designed to dynamically create and manage CrewAI agents, tasks, and crews based on specification documents. The system provides a flexible way to define and generate AI agent implementations using either JSON specifications or natural language descriptions.

## System Architecture

### Core Components

1. **Specification Parser (`core.py`)**
   - Entry point: `generate_crew_ai()`
   - Handles both JSON and natural language specifications
   - Converts specifications into structured CrewAISpec objects

2. **Data Models (`models/crew_agents.py`)**
   - Defines the core data structures:
     - `ToolParameter`: Configuration for tool parameters
     - `ToolConfig`: Tool definitions and implementations
     - `AgentRole`: Agent role specifications
     - `AgentConfig`: Complete agent configurations
     - `TaskDependency`: Task dependency definitions
     - `TaskConfig`: Task specifications
     - `CrewConfig`: Crew configuration and management
     - `CrewAISpec`: Complete specification for the entire system

3. **Generators**
   - Located in the `generators/` directory
   - Responsible for converting specifications into executable CrewAI implementations

4. **Utilities**
   - Located in the `utils/` directory
   - Provides helper functions for file operations and parsing

## Workflow

1. **Specification Creation**
   - Create a specification file (either JSON or natural language)
   - Place it in the `agent_spec/` directory
   - Example: `sample_research_crew.txt`

2. **Generation Process**
   ```python
   specification_file -> parse_specification() -> CrewAISpec -> generate_crew() -> Implementation Files
   ```

3. **Implementation Output**
   - Generated files are placed in the specified output directory
   - Includes:
     - Agent implementation files
     - Task definitions
     - Crew configurations
     - Requirements file
     - Environment configuration

## Testing and Execution

### Starting Point
The main entry point for testing is `test_crew_generation.py`, which demonstrates how to:
1. Load a specification file
2. Generate CrewAI implementation
3. Execute the generated crew

### Example Usage
```python
from meta_agent.core import generate_crew_ai

# Generate implementation from specification
implementation = generate_crew_ai(
    specification="path/to/spec_file.txt",
    output_dir="output/crew_name"
)
```

### Running Generated Crews
1. Navigate to the output directory
2. Install requirements: `pip install -r requirements.txt`
3. Configure environment variables in `.env`
4. Execute: `python generated_agent.py`

## Specification Format

### JSON Format
```json
{
    "name": "CrewName",
    "description": "Crew description",
    "instructions": "Detailed instructions",
    "agents": [...],
    "tasks": [...],
    "crew": {...},
    "tools": [...]
}
```

### Natural Language Format
```
Name: CrewName
Description: Crew description
Tools Needed:
1. Tool1: Description
   - parameter1 (string): Parameter description
   - parameter2 (int): Parameter description

Agents:
[Agent definitions]

Tasks:
[Task definitions]
```

## Best Practices

1. **Specification Design**
   - Provide clear and detailed descriptions
   - Define explicit tool parameters
   - Specify dependencies between tasks
   - Include proper error handling

2. **Testing**
   - Test specifications with sample data
   - Verify generated implementations
   - Check for proper tool integration
   - Validate task dependencies

3. **Maintenance**
   - Keep specifications up-to-date
   - Document any custom tools
   - Monitor agent performance
   - Update dependencies as needed 