"""
Core functionality for the Meta Agent.

This module contains the main entry point for generating agents from specifications.
"""

import json
from typing import Dict, Any, Optional, Union, List
from pathlib import Path

from meta_agent.models import (
    CrewAISpec,
    parse_crew_ai_spec
)

from meta_agent.generators.crew_ai.main_generator import (
    parse_natural_language_spec,
    parse_spec_file,
    generate_implementation as generate_crew
)

from meta_agent.utils import parse_json_string, ensure_directory, write_file

def generate_crew_ai(specification: Union[str, Path], output_dir: Optional[str] = None) -> Dict[str, str]:
    """
    Generate a CrewAI implementation based on a specification file.
    
    Args:
        specification: Path to the specification file or the specification string itself.
                      Can be either a JSON string/file or a natural language description.
        output_dir: Optional directory to write the generated files to.
                   If provided, the implementation files will be written to this directory.
    
    Returns:
        Dictionary mapping file names to their generated content
        
    Raises:
        ValueError: If the specification is empty or invalid
        FileNotFoundError: If the specification file doesn't exist
        Exception: If any step of the generation process fails
    """
    # Check if specification is a file path or a string
    if isinstance(specification, (str, Path)) and Path(specification).exists():
        crew_spec = parse_spec_file(specification)
    else:
        # Check for empty specification
        if not specification or not specification.strip():
            raise ValueError("CrewAI specification cannot be empty")
        
        # Parse the specification string
        try:
            # Try parsing as JSON first
            try:
                spec_dict = json.loads(specification)
                crew_spec = parse_natural_language_spec(json.dumps(spec_dict))
            except json.JSONDecodeError:
                # If not JSON, treat as natural language
                crew_spec = parse_natural_language_spec(specification)
        except Exception as e:
            raise ValueError(f"Failed to parse CrewAI specification: {str(e)}")
    
    print(f"Generating implementation for {crew_spec.name}")
    print(f"Spec: {crew_spec}")
    
    # Generate the implementation
    try:
        implementation = generate_crew(crew_spec, output_dir)
    except Exception as e:
        raise Exception(f"Failed to generate CrewAI implementation: {str(e)}")
    
    return implementation

def parse_natural_language_spec(content: str) -> CrewAISpec:
    """
    Parse a natural language specification into a CrewAISpec object.
    
    Args:
        content: Natural language specification content
        
    Returns:
        CrewAISpec object containing the parsed specification
        
    Raises:
        ValueError: If required sections are missing or invalid
    """
    # Split sections
    sections = {}
    current_section = None
    current_content = []
    
    for line in content.split('\n'):
        if line.strip() and not line.startswith(' '):
            if line.strip().lower().endswith(':'):
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line.strip(':').strip()
                current_content = []
            else:
                if current_content:
                    current_content.append(line)
        else:
            if current_content:
                current_content.append(line)
    
    if current_section:
        sections[current_section] = '\n'.join(current_content).strip()
    
    # Validate required sections
    required_sections = ['Name', 'Description', 'Agents', 'Tasks', 'Crew']
    missing_sections = [s for s in required_sections if s not in sections]
    if missing_sections:
        raise ValueError(f"Missing required sections: {', '.join(missing_sections)}")
    
    # Convert natural language sections to dictionary format
    spec_dict = {
        'name': sections.get('Name', '').strip(),
        'description': sections.get('Description', '').strip(),
        'instructions': sections.get('Instructions', '').strip(),
        'agents': _parse_agents_section(sections.get('Agents', '')),
        'tasks': _parse_tasks_section(sections.get('Tasks', '')),
        'crew': _parse_crew_section(sections.get('Crew', '')),
        'tools': _parse_tools_section(sections.get('Tools', ''))
    }
    
    return parse_crew_ai_spec(spec_dict)

def _parse_tasks_section(section: str) -> List[Dict[str, Any]]:
    """Parse the Tasks section into a list of task configurations."""
    tasks = []
    current_task = None
    
    for line in section.split('\n'):
        line = line.strip()
        if line.startswith('- Name:'):
            if current_task:
                tasks.append(current_task)
            current_task = {
                'name': line.split(':', 1)[1].strip(),
                'description': '',
                'expected_output': None,
                'dependencies': [],
                'tools': [],
                'context': '',
                'agent': None,
                'async_execution': False
            }
        elif line.startswith('    Description:') and current_task:
            current_task['description'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Output:') and current_task:
            current_task['expected_output'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Dependencies:') and current_task:
            deps = line.split(':', 1)[1].strip()
            if deps.lower() != 'none':
                current_task['dependencies'] = [
                    {'task_name': d.strip()} 
                    for d in deps.split(',')
                ]
        elif line.startswith('    Agent:') and current_task:
            current_task['agent'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Tools:') and current_task:
            tools = line.split(':', 1)[1].strip()
            if tools.lower() != 'none':
                current_task['tools'] = [t.strip() for t in tools.split(',')]
        elif line.startswith('    Context:') and current_task:
            current_task['context'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Async:') and current_task:
            current_task['async_execution'] = line.split(':', 1)[1].strip().lower() == 'true'
    
    if current_task:
        tasks.append(current_task)
    
    # Validate tasks
    for task in tasks:
        if not task['name']:
            raise ValueError("Task name is required")
        if not task['description']:
            raise ValueError(f"Description is required for task '{task['name']}'")
    
    return tasks

def _parse_crew_section(section: str) -> Dict[str, Any]:
    """Parse the Crew section into a crew configuration."""
    crew = {
        'name': '',
        'description': '',
        'agents': [],
        'tasks': [],
        'process': 'sequential',
        'verbose': True,
        'manager_id': None,
        'task_execution_settings': {
            'timeout': 3600,
            'max_retries': 2,
            'retry_delay': 300
        }
    }
    
    for line in section.split('\n'):
        line = line.strip()
        if line.startswith('- Name:'):
            crew['name'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Description:'):
            crew['description'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Agents:'):
            agents = line.split(':', 1)[1].strip()
            crew['agents'] = [
                {'name': a.strip()} 
                for a in agents.split(',')
            ]
        elif line.startswith('- Tasks:'):
            tasks = line.split(':', 1)[1].strip()
            crew['tasks'] = [
                {'name': t.strip()} 
                for t in tasks.split(',')
            ]
        elif line.startswith('- Process:'):
            process = line.split(':', 1)[1].strip().lower()
            if process in ['sequential', 'hierarchical', 'parallel']:
                crew['process'] = process
        elif line.startswith('- Manager:'):
            crew['manager_id'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Settings:'):
            settings = {}
            for setting_line in section.split('\n'):
                if setting_line.strip().startswith('    - Timeout:'):
                    settings['timeout'] = int(setting_line.split(':', 1)[1].strip())
                elif setting_line.strip().startswith('    - MaxRetries:'):
                    settings['max_retries'] = int(setting_line.split(':', 1)[1].strip())
                elif setting_line.strip().startswith('    - RetryDelay:'):
                    settings['retry_delay'] = int(setting_line.split(':', 1)[1].strip())
            if settings:
                crew['task_execution_settings'].update(settings)
    
    # Validate crew configuration
    if not crew['name']:
        raise ValueError("Crew name is required")
    if not crew['agents']:
        raise ValueError("At least one agent must be specified in the crew")
    if not crew['tasks']:
        raise ValueError("At least one task must be specified in the crew")
    
    return crew
