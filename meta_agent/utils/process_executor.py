import asyncio
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Any, Callable, Optional

# Global process pool executor (can be replaced with other backends)
process_pool: Optional[ProcessPoolExecutor] = None

def get_process_pool(max_workers: int = 2) -> ProcessPoolExecutor:
    global process_pool
    if process_pool is None:
        process_pool = ProcessPoolExecutor(max_workers=max_workers)
    return process_pool

def _call_func_with_args(func, args, kwargs):
    return func(*args, **kwargs)

async def run_in_process(func: Callable, *args, **kwargs) -> Any:
    """
    Run a blocking function in a separate process and return the result (awaitable).
    Can be replaced with other backends (e.g., Celery) for plug-and-play.
    """
    loop = asyncio.get_event_loop()
    pool = get_process_pool()
    return await loop.run_in_executor(pool, _call_func_with_args, func, args, kwargs) 