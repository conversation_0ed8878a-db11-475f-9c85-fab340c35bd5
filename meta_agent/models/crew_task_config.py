from typing import List, Optional, Dict, Any, Union, Type
from pydantic import BaseModel, Field
from crewai import Task

class TaskConfig(BaseModel):
    # Required parameters
    description: str = Field(..., description="Clear, concise statement of what the task entails")
    agent: str = Field(..., description="Name of the agent responsible for executing the task")
    
    # Optional parameters with defaults
    name: Optional[str] = Field(default=None, description="Name identifier for the task")
    expected_output: Optional[str] = Field(default=None, description="Detailed description of what task completion looks like")
    tools: Optional[List[Any]] = Field(default=[], description="Tools/resources the agent is limited to use")
    context: Optional[List[str]] = Field(default=[], description="Other tasks whose outputs will be used as context")
    
    # Advanced configuration
    async_execution: bool = Field(default=False, description="Whether the task should be executed asynchronously")
    human_input: bool = Field(default=False, description="Whether the task should have human review")
    config: Optional[Dict[str, Any]] = Field(default=None, description="Task-specific configuration parameters")
    
    # Output handling
    output_file: Optional[str] = Field(default=None, description="File path for storing task output")
    output_json: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model to structure JSON output")
    output_pydantic: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model for task output")
    create_directory: bool = Field(default=False, description="Create directories when saving output file")
    
    # Task guardrails
    guardrail: Optional[Any] = Field(default=None, description="Function to validate/transform task output")
    max_retries: int = Field(default=3, description="Maximum number of retries for task execution")
    
    # Callback
    callback: Optional[Any] = Field(default=None, description="Function to be executed after task completion")
    
    class Config:
        arbitrary_types_allowed = True 