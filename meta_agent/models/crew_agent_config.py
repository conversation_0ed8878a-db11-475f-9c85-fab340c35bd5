from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class LLMConfig(BaseModel):
    model: str = Field(default="gpt-4", description="The LLM model to use")
    base_url: Optional[str] = Field(default=None, description="Base URL for the LLM API")
    api_key: Optional[str] = Field(default=None, description="API key for the LLM service")
    temperature: float = Field(default=0.7, description="Temperature for model responses")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens for responses")

class AgentConfig(BaseModel):
    # Required parameters
    name: str = Field(..., description="Name of the agent")
    role: str = Field(..., description="Role of the agent")
    goal: str = Field(..., description="Primary goal of the agent")
    
    # Optional parameters with defaults
    backstory: Optional[str] = Field(default=None, description="Background story of the agent")
    tools: Optional[List[Any]] = Field(default=[], description="List of tools the agent can use")
    verbose: bool = Field(default=True, description="Enable verbose logging")
    allow_delegation: bool = Field(default=False, description="Allow task delegation to other agents")
    
    # Advanced configuration
    llm: Optional[LLMConfig] = Field(default=None, description="LLM configuration")
    memory: bool = Field(default=False, description="Enable memory for the agent")
    max_rpm: Optional[int] = Field(default=None, description="Maximum requests per minute")
    max_iter: int = Field(default=25, description="Maximum iterations for task execution")
    max_execution_time: Optional[int] = Field(default=None, description="Maximum execution time in seconds")
    max_retry_limit: int = Field(default=3, description="Maximum number of retries")
    respect_context_window: bool = Field(default=True, description="Respect context window limits")
    
    # Template configurations
    system_template: Optional[str] = Field(default=None, description="Custom system template")
    prompt_template: Optional[str] = Field(default=None, description="Custom prompt template")
    response_template: Optional[str] = Field(default=None, description="Custom response template")
    use_system_prompt: bool = Field(default=True, description="Use system prompt during execution")
    
    # Additional settings
    cache: bool = Field(default=False, description="Enable caching for tool usage")
    step_callback: Optional[Any] = Field(default=None, description="Callback function for step monitoring")
    knowledge_sources: Optional[List[str]] = Field(default=[], description="List of knowledge sources")
    embedder_config: Optional[Dict[str, Any]] = Field(default=None, description="Configuration for embedding model") 