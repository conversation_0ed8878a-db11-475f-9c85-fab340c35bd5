"""
Data models for CrewAI components (agents, tools, tasks, and crews).
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

@dataclass
class ToolParameter:
    """Model for tool parameter configuration."""
    name: str
    type: str
    description: str
    required: bool = True
    default: Optional[Any] = None

@dataclass
class ToolConfig:
    """Model for tool configuration."""
    name: str
    description: str
    parameters: List[ToolParameter]
    return_type: str = "string"
    implementation: Optional[str] = None

@dataclass
class AgentRole:
    """Model for agent role configuration."""
    title: str
    description: str
    responsibilities: List[str]
    expertise: List[str]

@dataclass
class AgentConfig:
    """Model for agent configuration."""
    name: str
    role: str
    tools: List[ToolConfig]
    goal: str
    backstory: str
    allow_delegation: bool = False
    verbose: bool = True
    memory: bool = True
    llm_config: Optional[Dict[str, Any]] = None


@dataclass
class TaskDependency:
    """Model for task dependency configuration."""
    task_name: str
    required: bool = True
    condition: Optional[str] = None

@dataclass
class TaskConfig:
    """Model for task configuration."""
    name: str
    description: str
    agent: str
    expected_output: Optional[str] = None
    tools: Optional[List[str]] = None
    dependencies: Optional[List[TaskDependency]] = None
    context: Optional[str] = None
    async_execution: bool = False
    priority: int = 0

@dataclass
class CrewConfig:
    """Model for crew configuration."""
    name: str
    description: str
    agents: List[AgentConfig]
    tasks: List[TaskConfig]
    process: str = "sequential"  # or "hierarchical"
    manager_id: Optional[str] = None
    verbose: bool = True
    max_concurrent_tasks: int = 1

@dataclass
class CrewAISpec:
    """Model for complete CrewAI specification."""
    name: str
    description: str
    instructions: str
    agents: List[AgentConfig]
    tasks: List[TaskConfig]
    crew: CrewConfig
    tools: List[ToolConfig]
    metadata: Optional[Dict[str, Any]] = None

def _get_case_insensitive(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """Get a value from a dictionary using case-insensitive key matching."""
    key_lower = key.lower()
    for k in data:
        if k.lower() == key_lower:
            return data[k]
    return default

def parse_tool_parameter(data: Dict[str, Any]) -> ToolParameter:
    """Parse a tool parameter configuration."""
    return ToolParameter(
        name=data.get('name', ''),
        type=data.get('type', 'string'),
        description=data.get('description', ''),
        required=data.get('required', True)
    )

def parse_tool_config(data: Dict[str, Any]) -> ToolConfig:
    """Parse dictionary into ToolConfig."""
    if isinstance(data, str):
        return ToolConfig(name=data, description='', parameters=[])
    
    # Handle tools specified as a dictionary with name as key and description as value
    if len(data) == 1 and isinstance(next(iter(data.values())), str):
        name = next(iter(data.keys()))
        description = next(iter(data.values()))
        return ToolConfig(name=name, description=description, parameters=[])
    
    parameters = []
    params_data = _get_case_insensitive(data, 'parameters', {})
    
    # Handle parameters as a dictionary
    if isinstance(params_data, dict):
        for param_name, param_info in params_data.items():
            if isinstance(param_info, str):
                # Simple type definition
                parameters.append(ToolParameter(
                    name=param_name,
                    type=param_info,
                    description='',
                    required=True
                ))
            elif isinstance(param_info, dict):
                # Full parameter definition
                parameters.append(ToolParameter(
                    name=param_name,
                    type=_get_case_insensitive(param_info, 'type', 'string'),
                    description=_get_case_insensitive(param_info, 'description', ''),
                    required=_get_case_insensitive(param_info, 'required', True)
                ))
    # Handle parameters as a list
    elif isinstance(params_data, list):
        for param in params_data:
            if isinstance(param, dict):
                parameters.append(ToolParameter(
                    name=_get_case_insensitive(param, 'name', ''),
                    type=_get_case_insensitive(param, 'type', 'string'),
                    description=_get_case_insensitive(param, 'description', ''),
                    required=_get_case_insensitive(param, 'required', True)
                ))
    
    return ToolConfig(
        name=_get_case_insensitive(data, 'name', ''),
        description=_get_case_insensitive(data, 'description', ''),
        parameters=parameters,
        return_type=_get_case_insensitive(data, 'return_type', 'string'),
        implementation=_get_case_insensitive(data, 'implementation')
    )

def parse_agent_role(data: Dict[str, Any]) -> AgentRole:
    """Parse an agent role configuration."""
    return AgentRole(
        title=data.get('title', ''),
        description=data.get('description', ''),
        responsibilities=data.get('responsibilities', []),
        expertise=data.get('expertise', [])
    )

def parse_agent_config(data: Dict[str, Any]) -> AgentConfig:
    """Parse dictionary into AgentConfig."""
    # Handle case-insensitive keys
    tools_data = _get_case_insensitive(data, 'tools', {})
    
    # Handle tools - could be a list or a dictionary
    tools = []
    if isinstance(tools_data, dict):
        # Handle tools as dictionary with name:description pairs
        for tool_name, tool_desc in tools_data.items():
            tools.append(ToolConfig(
                name=tool_name,
                description=tool_desc if isinstance(tool_desc, str) else '',
                parameters=[]
            ))
    elif isinstance(tools_data, list):
        # Handle tools as list
        for tool in tools_data:
            if isinstance(tool, str):
                tools.append(ToolConfig(name=tool, description='', parameters=[]))
            elif isinstance(tool, dict):
                tools.append(parse_tool_config(tool))
    
    return AgentConfig(
        name=_get_case_insensitive(data, 'name', ''),
        role=_get_case_insensitive(data, 'role', ''),
        tools=tools,
        goal=_get_case_insensitive(data, 'goal', ''),
        backstory=_get_case_insensitive(data, 'backstory', ''),
        allow_delegation=_get_case_insensitive(data, 'allow_delegation', False),
        verbose=_get_case_insensitive(data, 'verbose', True),
        memory=_get_case_insensitive(data, 'memory', True),
        llm_config=_get_case_insensitive(data, 'llm_config')
    )

def parse_task_dependency(data: Dict[str, Any]) -> TaskDependency:
    """Parse a task dependency configuration."""
    if isinstance(data, str):
        return TaskDependency(task_name=data)
    return TaskDependency(task_name=data.get('task_name', ''))

def parse_task_config(data: Dict[str, Any]) -> TaskConfig:
    """Parse dictionary into TaskConfig."""
    # Handle dependencies
    deps_data = _get_case_insensitive(data, 'dependencies', [])
    dependencies = []
    
    if isinstance(deps_data, str):
        # Handle single dependency as string
        if deps_data.lower() != 'none':
            dependencies = [TaskDependency(task_name=deps_data)]
    elif isinstance(deps_data, list):
        # Handle list of dependencies
        dependencies = [
            parse_task_dependency(dep) if isinstance(dep, (dict, str)) else TaskDependency(task_name='')
            for dep in deps_data
        ]
    
    return TaskConfig(
        name=_get_case_insensitive(data, 'name', ''),
        description=_get_case_insensitive(data, 'description', ''),
        agent=_get_case_insensitive(data, 'agent', ''),
        expected_output=_get_case_insensitive(data, 'output', None),
        tools=_get_case_insensitive(data, 'tools'),
        dependencies=dependencies,
        context=_get_case_insensitive(data, 'context'),
        async_execution=_get_case_insensitive(data, 'async_execution', False),
        priority=_get_case_insensitive(data, 'priority', 0)
    )

def parse_crew_config(data: Dict[str, Any], all_agents: Optional[List[AgentConfig]] = None) -> CrewConfig:
    """Parse dictionary into CrewConfig.
    
    Args:
        data: Dictionary containing crew configuration
        all_agents: Optional list of all available agents to resolve agent references
    """
    # Handle agent references
    agent_refs = _get_case_insensitive(data, 'agents', [])
    agents = []
    
    for agent_ref in agent_refs:
        if isinstance(agent_ref, dict):
            agents.append(parse_agent_config(agent_ref))
        elif isinstance(agent_ref, str) and all_agents:
            # Find agent by name in all_agents list
            matching_agent = next((a for a in all_agents if a.name == agent_ref), None)
            if matching_agent:
                agents.append(matching_agent)
            else:
                # If agent not found, create a minimal agent config
                agents.append(AgentConfig(
                    name=agent_ref,
                    role=AgentRole(title=agent_ref, description='', responsibilities=[], expertise=[]),
                    tools=[],
                    goal='',
                    backstory=''
                ))
        else:
            # Create minimal agent config for string references when all_agents not provided
            agents.append(AgentConfig(
                name=str(agent_ref),
                role=AgentRole(title=str(agent_ref), description='', responsibilities=[], expertise=[]),
                tools=[],
                goal='',
                backstory=''
            ))
    
    # Handle task references
    task_refs = _get_case_insensitive(data, 'tasks', [])
    tasks = []
    
    for task_ref in task_refs:
        if isinstance(task_ref, dict):
            tasks.append(parse_task_config(task_ref))
        else:
            # For string task references, create minimal task config
            tasks.append(TaskConfig(
                name=str(task_ref),
                description='',
                agent='',  # This will be set later
                expected_output=None
            ))
    
    return CrewConfig(
        name=_get_case_insensitive(data, 'name', ''),
        description=_get_case_insensitive(data, 'description', ''),
        agents=agents,
        tasks=tasks,
        process=_get_case_insensitive(data, 'process', 'sequential'),
        manager_id=_get_case_insensitive(data, 'manager_id'),
        verbose=_get_case_insensitive(data, 'verbose', True),
        max_concurrent_tasks=_get_case_insensitive(data, 'max_concurrent_tasks', 1)
    )

def parse_crew_ai_spec(data: Dict[str, Any]) -> CrewAISpec:
    """Parse dictionary into CrewAISpec."""
    # Parse agents first so we can use them to resolve references in crew config
    agents = [parse_agent_config(a) for a in _get_case_insensitive(data, 'agents', [])]
    
    return CrewAISpec(
        name=_get_case_insensitive(data, 'name', ''),
        description=_get_case_insensitive(data, 'description', ''),
        instructions=_get_case_insensitive(data, 'instructions', ''),
        agents=agents,
        tasks=[parse_task_config(t) for t in _get_case_insensitive(data, 'tasks', [])],
        crew=parse_crew_config(_get_case_insensitive(data, 'crew', {}), all_agents=agents),
        tools=[parse_tool_config(t) for t in _get_case_insensitive(data, 'tools', [])],
        metadata=_get_case_insensitive(data, 'metadata')
    ) 