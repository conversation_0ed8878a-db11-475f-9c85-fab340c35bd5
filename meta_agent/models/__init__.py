"""
Meta Agent models package.
"""

from .crew_agents import (
    # Data models
    ToolParameter as CrewToolParameter,
    Tool<PERSON>onfig as <PERSON><PERSON><PERSON><PERSON>onfig,
    Agent<PERSON><PERSON>,
    AgentConfig as CrewAgentConfig,
    TaskDependency,
    TaskConfig as CrewTaskConfig,
    CrewConfig,
    CrewAISpec,
    
    # Parser functions
    parse_tool_config,
    parse_agent_config,
    parse_task_config,
    parse_crew_config,
    parse_crew_ai_spec
)

__all__ = [
    # Original agent models
    'AgentSpecification',
    'AgentImplementation',
    'GuardrailDefinition',
    'OutputTypeDefinition',
    'OutputTypeField',
    'ToolDefinition',
    'ToolParameter',
    
    # CrewAI models
    'CrewToolParameter',
    'CrewToolConfig',
    'AgentRole',
    'CrewAgentConfig',
    'TaskDependency',
    'CrewTaskConfig',
    'CrewConfig',
    'CrewAISpec',
    
    # Parser functions
    'parse_tool_config',
    'parse_agent_config',
    'parse_task_config',
    'parse_crew_config',
    'parse_crew_ai_spec'
]
