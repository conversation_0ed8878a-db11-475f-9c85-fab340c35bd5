from typing import Optional
from crewai import LLM
import os

def get_azure_llm(
    model: str = "gpt-4",
    temperature: float = 0.7,
    max_tokens: Optional[int] = None
) -> LLM:
    """
    Create an Azure OpenAI LLM configuration.
    
    Args:
        model: The model to use (default: gpt-4)
        temperature: Temperature for model responses (default: 0.7)
        max_tokens: Maximum tokens for responses (default: None)
        
    Returns:
        LLM: Configured Azure OpenAI LLM instance
    """
    return LLM(
        model=model,
        base_url=os.environ["AZURE_OPENAI_ENDPOINT"],
        api_key=os.environ["AZURE_OPENAI_API_KEY"],
        temperature=temperature,
        max_tokens=max_tokens
    ) 