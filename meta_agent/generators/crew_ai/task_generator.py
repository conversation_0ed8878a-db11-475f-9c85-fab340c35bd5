"""
Task Generator for CrewAI - Creates tasks based on specifications.

This module follows the CrewAI documentation for creating tasks:
https://docs.crewai.com/concepts/tasks/
"""

import logging
from typing import Dict, Any, List, Optional, Union, Type, Tuple
from dataclasses import dataclass
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Any, Callable

class Task(BaseModel):
    description: str
    agent:Any
    name: Optional[str] = None
    expected_output: Any
    tools: Optional[List[Any]] = None
    context: Optional[List[str]] = None
    async_execution: Optional[bool] = None
    human_input: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None
    output_file: Optional[str] = None
    output_json: Optional[bool] = None
    output_pydantic: Optional[bool] = None
    create_directory: Optional[bool] = None
    guardrail: Optional[Any] = None
    max_retries: Optional[int] = None
    callback: Optional[Callable[..., Any]] = None
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskConfig(BaseModel):
    """Configuration model for CrewAI tasks with comprehensive validation."""
    
    # Required parameters
    description: str = Field(..., description="Clear, concise statement of what the task entails")
    agent: str = Field(..., description="Name of the agent responsible for executing the task")
    
    # Optional parameters with defaults
    name: Optional[str] = Field(default=None, description="Name identifier for the task")
    expected_output: Optional[str] = Field(default=None, description="Detailed description of what task completion looks like")
    tools: Optional[List[Any]] = Field(default=[], description="Tools/resources the agent is limited to use")
    context: Optional[List[str]] = Field(default=[], description="Other tasks whose outputs will be used as context")
    
    # Advanced configuration
    async_execution: bool = Field(default=False, description="Whether the task should be executed asynchronously")
    human_input: bool = Field(default=False, description="Whether the task should have human review")
    config: Optional[Dict[str, Any]] = Field(default=None, description="Task-specific configuration parameters")
    
    # Output handling
    output_file: Optional[str] = Field(default=None, description="File path for storing task output")
    output_json: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model to structure JSON output")
    output_pydantic: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model for task output")
    create_directory: bool = Field(default=False, description="Create directories when saving output file")
    
    # Task guardrails
    guardrail: Optional[Any] = Field(default=None, description="Function to validate/transform task output")
    max_retries: int = Field(default=3, description="Maximum number of retries for task execution")
    
    # Callback
    callback: Optional[Any] = Field(default=None, description="Function to be executed after task completion")
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('output_file')
    def validate_output_file(cls, v, values):
        """Validate output file path and create directory if needed."""
        if v and values.get('create_directory'):
            os.makedirs(os.path.dirname(v), exist_ok=True)
        return v
    
    @validator('context')
    def validate_context(cls, v):
        """Validate that context tasks exist."""
        if v and not all(isinstance(task, str) for task in v):
            raise ValueError("Context must be a list of task names")
        return v
    
    @validator('tools')
    def validate_tools(cls, v):
        """Validate that tools are properly configured."""
        if v and not all(hasattr(tool, 'name') for tool in v):
            raise ValueError("All tools must have a 'name' attribute")
        return v

def create_task(config: Union[Dict[str, Any], TaskConfig]) -> Optional[Task]:
    """
    Create a CrewAI task with comprehensive configuration and validation.
    
    Args:
        config: Either a dictionary or TaskConfig object containing task parameters
        
    Returns:
        Optional[Task]: Created CrewAI task or None if creation fails
        
    Raises:
        ValueError: If required parameters are missing or invalid
    """
    try:
        # Convert dict to TaskConfig if needed
        if isinstance(config, dict):
            config = TaskConfig(**config)
        
        # Validate required parameters
        if not config.description or not config.agent:
            raise ValueError("Description and agent are required parameters")
        
        # Validate output configuration
        if config.output_json and config.output_pydantic:
            raise ValueError("Cannot specify both output_json and output_pydantic")
        
        # Create task with all available attributes
        task = Task(
            description=config.description,
            agent=config.agent,
            name=config.name,
            expected_output=config.expected_output,
            tools=config.tools,
            context=config.context,
            async_execution=config.async_execution,
            human_input=config.human_input,
            config=config.config,
            output_file=config.output_file,
            output_json=config.output_json,
            output_pydantic=config.output_pydantic,
            create_directory=config.create_directory,
            guardrail=config.guardrail,
            max_retries=config.max_retries,
            callback=config.callback
        )
        
        logger.info(f"Successfully created task: {config.name or 'Unnamed Task'}")
        return task
        
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return None

def create_sequential_tasks(tasks: List[Union[Dict[str, Any], TaskConfig]]) -> List[Optional[Task]]:
    """
    Create multiple tasks with sequential dependencies.
    
    Args:
        tasks: List of task configurations
        
    Returns:
        List[Optional[Task]]: List of created tasks or None for failed tasks
    """
    created_tasks = []
    for task_config in tasks:
        task = create_task(task_config)
        created_tasks.append(task)
    return created_tasks

def create_parallel_tasks(tasks: List[Union[Dict[str, Any], TaskConfig]]) -> List[Optional[Task]]:
    """
    Create multiple tasks for parallel execution.
    
    Args:
        tasks: List of task configurations
        
    Returns:
        List[Optional[Task]]: List of created tasks or None for failed tasks
    """
    created_tasks = []
    for task_config in tasks:
        if isinstance(task_config, dict):
            task_config['async_execution'] = True
        else:
            task_config.async_execution = True
        task = create_task(task_config)
        created_tasks.append(task)
    return created_tasks

def create_sequential_task(
    name: str,
    description: str,
    agent_name: str,
    dependencies: List[str]
) -> str:
    """
    Generate code for a task that depends on other tasks.
    
    Args:
        name: Task name
        description: Task description
        agent_name: Name of the agent assigned to this task
        dependencies: List of task names this task depends on
        
    Returns:
        String containing the generated sequential task code
    """
    task_name = name.lower().replace(' ', '_')
    dependencies_list = ", ".join(dependencies)
    
    code = f"""from crewai import Task

{task_name} = Task(
    description=\"\"\"{description}\"\"\",
    agent={agent_name},
    dependencies=[{dependencies_list}]
)
"""
    return code

def create_research_task(
    name: str,
    topic: str,
    agent_name: str,
    output_format: Optional[str] = None
) -> str:
    """
    Generate code for a specialized research task.
    
    Args:
        name: Task name
        topic: Research topic
        agent_name: Name of the agent assigned to this task
        output_format: Optional format specification for the output
        
    Returns:
        String containing the generated research task code
    """
    task_name = name.lower().replace(' ', '_')
    
    description = f"""Research the topic: {topic}
    
1. Gather information from reliable sources
2. Analyze the findings
3. Synthesize the information
4. Present conclusions"""

    if output_format:
        description += f"\n\nProvide the output in the following format:\n{output_format}"
    
    code = f"""from crewai import Task

{task_name} = Task(
    description=\"\"\"{description}\"\"\",
    agent={agent_name},
    expected_output="A comprehensive research report on {topic}"
)
"""
    return code

def create_analysis_task(
    name: str,
    data_source: str,
    analysis_type: str,
    agent_name: str
) -> str:
    """
    Generate code for a data analysis task.
    
    Args:
        name: Task name
        data_source: Source of data to analyze
        analysis_type: Type of analysis to perform
        agent_name: Name of the agent assigned to this task
        
    Returns:
        String containing the generated analysis task code
    """
    task_name = name.lower().replace(' ', '_')
    
    description = f"""Analyze {data_source} using {analysis_type} approach.
    
Expected steps:
1. Load and prepare the data
2. Perform {analysis_type} analysis
3. Identify key patterns and insights
4. Generate actionable recommendations"""
    
    code = f"""from crewai import Task

{task_name} = Task(
    description=\"\"\"{description}\"\"\",
    agent={agent_name},
    expected_output=f"Analysis report detailing findings from {analysis_type} analysis of {data_source}"
)
"""
    return code 