import logging
from typing import Optional, Dict, Any, List
from crewai import Agent
from meta_agent.models.crew_agent_config import AgentConfig, LLMConfig
from meta_agent.llm.azure_llm import get_azure_llm

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_crew_agent(config: AgentConfig) -> Optional[Agent]:
    """
    Create a CrewAI agent with comprehensive configuration options.
    
    Args:
        config: AgentConfig object containing all agent parameters
        
    Returns:
        Optional[Agent]: Created CrewAI agent or None if creation fails
        
    Raises:
        ValueError: If required parameters are missing or invalid
    """
    try:
        # Validate required parameters
        if not config.name or not config.role or not config.goal:
            raise ValueError("Name, role, and goal are required parameters")
            
        # Generate default backstory if not provided
        if not config.backstory:
            config.backstory = f"An expert {config.role} with deep expertise in the field."
            
        # Configure LLM
        llm = None
        if config.llm:
            llm = get_azure_llm(
                model=config.llm.model,
                temperature=config.llm.temperature,
                max_tokens=config.llm.max_tokens
            )
            
        # Create agent with all available attributes
        agent = Agent(
            role=config.role,
            goal=config.goal,
            backstory=config.backstory,
            tools=config.tools,
            verbose=config.verbose,
            allow_delegation=config.allow_delegation,
            llm=llm,
            memory=config.memory,
            max_rpm=config.max_rpm,
            max_iter=config.max_iter,
            max_execution_time=config.max_execution_time,
            max_retry_limit=config.max_retry_limit,
            respect_context_window=config.respect_context_window,
            system_template=config.system_template,
            prompt_template=config.prompt_template,
            response_template=config.response_template,
            use_system_prompt=config.use_system_prompt,
            cache=config.cache,
            step_callback=config.step_callback,
            knowledge_sources=config.knowledge_sources,
            embedder_config=config.embedder_config
        )
        
        logger.info(f"Successfully created agent: {config.name}")
        return agent
        
    except Exception as e:
        logger.error(f"Error creating agent {config.name}: {str(e)}")
        return None

def create_agent_from_dict(config_dict: Dict[str, Any]) -> Optional[Agent]:
    """
    Create a CrewAI agent from a dictionary configuration.
    
    Args:
        config_dict: Dictionary containing agent configuration
        
    Returns:
        Optional[Agent]: Created CrewAI agent or None if creation fails
    """
    try:
        config = AgentConfig(**config_dict)
        return create_crew_agent(config)
    except Exception as e:
        logger.error(f"Error creating agent from dict: {str(e)}")
        return None 