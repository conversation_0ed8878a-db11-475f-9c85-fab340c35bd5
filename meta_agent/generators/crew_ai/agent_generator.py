"""
Agent Generator for CrewAI - Creates agents based on specifications.

This module follows the CrewAI documentation for creating effective agents:
https://docs.crewai.com/how-to/customizing-agents/
"""

from typing import Dict, Any, List, Optional
from meta_agent.models import CrewAgentConfig


def create_agent(spec: Dict[str, Any]) -> str:
    """
    Generate CrewAI agent code from specification.
    
    Args:
        spec: Agent specification containing role, responsibilities, and tools
        
    Returns:
        String containing the generated agent code
    """
    # Extract agent details
    name = spec['name'].lower().replace(' ', '_')
    role = spec['role']
    responsibilities = spec.get('responsibilities', [])
    goal = f"Execute tasks based on: {', '.join(responsibilities)}"
    backstory = f"An expert {role} with deep expertise in the field."
    
    # Generate agent code
    code = f"""from crewai import Agent, LLM

# Create {name} agent
{name} = Agent(
    role="{role}",
    goal="{goal}",
    backstory="{backstory}",
    verbose=True,
    allow_delegation=False,
    llm=LLM(
        model="gpt-4",
        base_url=os.environ["AZURE_OPENAI_ENDPOINT"],
        api_key=os.environ["AZURE_OPENAI_API_KEY"]
    )
)
"""
    return code

def create_expert_agent(
    name: str,
    role: str,
    goal: str,
    backstory: str,
    tools: Optional[List] = None
) -> str:
    """
    Generate code for an expert agent with specific capabilities.
    
    Args:
        name: Agent name
        role: Agent's role/expertise
        goal: Agent's primary goal
        backstory: Agent's background story
        tools: Optional list of tools the agent can use
        
    Returns:
        String containing the generated expert agent code
    """
    agent_name = name.lower().replace(' ', '_')
    
    tools_code = ""
    if tools:
        tools_list = "[" + ", ".join(t.name for t in tools) + "]"
        tools_code = f"    tools={tools_list},\n"
    
    code = f"""from crewai import Agent, LLM

# Create expert {agent_name} agent
{agent_name} = Agent(
    role="{role}",
    goal="{goal}",
    backstory="{backstory}",
    tools={tools_list},
    verbose=True,
    allow_delegation=False,
    llm=LLM(
        model="gpt-4",
        base_url=os.environ["AZURE_OPENAI_ENDPOINT"],
        api_key=os.environ["AZURE_OPENAI_API_KEY"]
    )
)
"""
    return code

def create_researcher_agent(
    name: str,
    research_topic: str,
    additional_goals: Optional[List[str]] = None
) -> str:
    """
    Generate code for a specialized research agent.
    
    Args:
        name: Agent name
        research_topic: Primary research topic
        additional_goals: Optional list of additional research goals
        
    Returns:
        String containing the generated researcher agent code
    """
    agent_name = name.lower().replace(' ', '_')
    goals = [f"Research and analyze {research_topic}"]
    if additional_goals:
        goals.extend(additional_goals)
    
    goal_str = ". ".join(goals)
    
    code = f"""from crewai import Agent
from langchain_openai import ChatOpenAI

# Create researcher agent
{agent_name} = Agent(
    role="Research Specialist",
    goal="{goal_str}",
    backstory=f"Expert researcher with extensive experience in {research_topic}. "
             f"Known for thorough analysis and comprehensive insights.",
    verbose=True,
    allow_delegation=False,
    # Using GPT-4 for enhanced capabilities
    llm=ChatOpenAI(
        model="gpt-4",
        temperature=0.7
    )
)
"""
    return code 