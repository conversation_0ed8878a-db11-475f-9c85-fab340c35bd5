"""
CrewAI generator package.

This package contains modules for generating CrewAI implementations.
"""

from .agent_generator import (
    create_agent,
    create_expert_agent,
    create_researcher_agent,
)
from .tool_generator import (
    create_basic_tool,
    create_api_tool,
    create_search_tool,
    create_file_tool,   
    ToolConfig
)
from .task_generator import (
    create_task,
    create_sequential_task,
    create_research_task,
    create_analysis_task,
    TaskConfig
)
from .crew_generator import (
    create_crew,
    create_sequential_crew,
    create_hierarchical_crew,
    create_custom_process_crew,
    CrewConfig
)
from .main_generator import (
    generate_implementation,
    parse_natural_language_spec,
    parse_spec_file
)

__all__ = [
    # Agent generator
    'create_agent',
    'create_expert_agent',
    'create_researcher_agent',
    
    # Tool generator
    'create_basic_tool',
    'create_api_tool',
    'create_search_tool',
    'create_file_tool',
    'ToolConfig',
    
    # Task generator
    'create_task',
    'create_sequential_task',
    'create_research_task',
    'create_analysis_task',
    'TaskConfig',
    
    # Crew generator
    'create_crew',
    'create_sequential_crew',
    'create_hierarchical_crew',
    'create_custom_process_crew',
    'CrewConfig',
    
    # Main generator
    'generate_implementation',
    'parse_natural_language_spec',
    'parse_spec_file'
] 