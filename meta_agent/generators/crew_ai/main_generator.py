"""
Main Generator for CrewAI - Combines agents, tools, tasks, and crews.

This module provides a unified interface for generating complete CrewAI implementations
from specifications in both JSON and natural language formats.
"""

import os
import json
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ...models.crew_agents import (
    CrewAISpec,
    parse_crew_ai_spec,
    parse_tool_config,
    parse_agent_config,
    parse_task_config,
    parse_crew_config
)
from .agent_generator import create_agent, create_expert_agent
from .tool_generator import create_basic_tool, create_api_tool
from .task_generator import create_task, create_sequential_task
from .crew_generator import create_crew, create_sequential_crew

def parse_spec_file(spec_file: Union[str, Path]) -> CrewAISpec:
    """
    Parse a specification file into a CrewAISpec object.
    Supports both JSON and natural language formats.
    
    Args:
        spec_file: Path to the specification file
        
    Returns:
        CrewAISpec object containing the parsed specification
        
    Raises:
        ValueError: If the file format is invalid or required sections are missing
    """
    with open(spec_file, 'r') as f:
        content = f.read()
    
    # Try parsing as JSON first
    try:
        spec_dict = json.loads(content)
        return parse_crew_ai_spec(spec_dict)
    except json.JSONDecodeError:
        # If not JSON, treat as natural language
        return parse_natural_language_spec(content)

def parse_natural_language_spec(content: str) -> CrewAISpec:
    """
    Parse a natural language specification into a CrewAISpec object.
    
    Args:
        content: Natural language specification content
        
    Returns:
        CrewAISpec object containing the parsed specification
        
    Raises:
        ValueError: If required sections are missing or invalid
    """
    # Split sections
    sections = {}
    current_section = None
    current_content = []
    
    for line in content.split('\n'):
        if line.strip() and not line.startswith(' '):
            if line.strip().lower().endswith(':'):
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line.strip(':').strip()
                current_content = []
            else:
                if current_content:
                    current_content.append(line)
        else:
            if current_content:
                current_content.append(line)
    
    if current_section:
        sections[current_section] = '\n'.join(current_content).strip()
    
    # Validate required sections
    required_sections = ['Name', 'Description', 'Agents', 'Tasks', 'Crew']
    missing_sections = [s for s in required_sections if s not in sections]
    if missing_sections:
        raise ValueError(f"Missing required sections: {', '.join(missing_sections)}")
    
    # Convert natural language sections to dictionary format
    spec_dict = {
        'name': sections.get('Name', '').strip(),
        'description': sections.get('Description', '').strip(),
        'instructions': sections.get('Instructions', '').strip(),
        'agents': _parse_agents_section(sections.get('Agents', '')),
        'tasks': _parse_tasks_section(sections.get('Tasks', '')),
        'crew': _parse_crew_section(sections.get('Crew', '')),
        'tools': _parse_tools_section(sections.get('Tools', ''))
    }
    
    return parse_crew_ai_spec(spec_dict)

def _parse_agents_section(section: str) -> List[Dict[str, Any]]:
    """Parse the Agents section into a list of agent configurations."""
    agents = []
    current_agent = {}
    
    for line in section.split('\n'):
        line = line.strip()
        if line.startswith('- Name:'):
            if current_agent:
                agents.append(current_agent)
            current_agent = {
                'name': line.split(':', 1)[1].strip(),
                'role': {
                    'title': '',
                    'description': '',
                    'responsibilities': [],
                    'expertise': []
                },
                'tools': [],
                'goal': '',
                'backstory': ''
            }
        elif line.startswith('    Role:'):
            role_title = line.split(':', 1)[1].strip()
            current_agent['role']['title'] = role_title
            current_agent['role']['description'] = f"Expert {role_title} with extensive experience"
            current_agent['backstory'] = f"Experienced {role_title} with a proven track record"
        elif line.startswith('    Responsibilities:'):
            continue  # Skip the header
        elif line.startswith('      - ') and current_agent:
            responsibility = line.strip('- ').strip()
            current_agent['role']['responsibilities'].append(responsibility)
            # Add to expertise as well
            if responsibility not in current_agent['role']['expertise']:
                current_agent['role']['expertise'].append(responsibility)
        elif line.startswith('    Tools:'):
            continue  # Skip the header
        elif line.startswith('      - ') and current_agent:
            tool = line.strip('- ').strip()
            if ':' in tool:
                tool_name, tool_desc = tool.split(':', 1)
                current_agent['tools'].append({
                    'name': tool_name.strip(),
                    'description': tool_desc.strip()
                })
            else:
                current_agent['tools'].append({'name': tool.strip()})
    
    if current_agent:
        agents.append(current_agent)
    
    # Set goals based on roles and responsibilities
    for agent in agents:
        agent['goal'] = f"Serve as {agent['role']['title']} and accomplish tasks effectively using expertise in: {', '.join(agent['role']['expertise'])}"
    
    return agents

def _parse_tasks_section(section: str) -> List[Dict[str, Any]]:
    """Parse the Tasks section into a list of task configurations."""
    tasks = []
    current_task = None
    
    for line in section.split('\n'):
        line = line.strip()
        if line.startswith('- Name:'):
            if current_task:
                tasks.append(current_task)
            current_task = {
                'name': line.split(':', 1)[1].strip(),
                'description': '',
                'expected_output': None,
                'dependencies': [],
                'tools': [],
                'context': '',
                'agent': None,
                'async_execution': False
            }
        elif line.startswith('    Description:') and current_task:
            current_task['description'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Output:') and current_task:
            current_task['expected_output'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Dependencies:') and current_task:
            deps = line.split(':', 1)[1].strip()
            if deps.lower() != 'none':
                current_task['dependencies'] = [
                    {'task_name': d.strip()} 
                    for d in deps.split(',')
                ]
        elif line.startswith('    Agent:') and current_task:
            current_task['agent'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Tools:') and current_task:
            tools = line.split(':', 1)[1].strip()
            if tools.lower() != 'none':
                current_task['tools'] = [t.strip() for t in tools.split(',')]
        elif line.startswith('    Context:') and current_task:
            current_task['context'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Async:') and current_task:
            current_task['async_execution'] = line.split(':', 1)[1].strip().lower() == 'true'
    
    if current_task:
        tasks.append(current_task)
    
    # Validate tasks
    for task in tasks:
        if not task['name']:
            raise ValueError("Task name is required")
        if not task['description']:
            raise ValueError(f"Description is required for task '{task['name']}'")
    
    return tasks

def _parse_crew_section(section: str) -> Dict[str, Any]:
    """Parse the Crew section into a crew configuration."""
    crew = {
        'name': '',
        'description': '',
        'agents': [],
        'tasks': [],
        'process': 'sequential',
        'verbose': True,
        'manager_id': None,
        'task_execution_settings': {
            'timeout': 3600,
            'max_retries': 2,
            'retry_delay': 300
        }
    }
    
    for line in section.split('\n'):
        line = line.strip()
        if line.startswith('- Name:'):
            crew['name'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Description:'):
            crew['description'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Agents:'):
            agents = line.split(':', 1)[1].strip()
            crew['agents'] = [
                {'name': a.strip()} 
                for a in agents.split(',')
            ]
        elif line.startswith('- Tasks:'):
            tasks = line.split(':', 1)[1].strip()
            crew['tasks'] = [
                {'name': t.strip()} 
                for t in tasks.split(',')
            ]
        elif line.startswith('- Process:'):
            process = line.split(':', 1)[1].strip().lower()
            if process in ['sequential', 'hierarchical', 'parallel']:
                crew['process'] = process
        elif line.startswith('- Manager:'):
            crew['manager_id'] = line.split(':', 1)[1].strip()
        elif line.startswith('- Settings:'):
            settings = {}
            for setting_line in section.split('\n'):
                if setting_line.strip().startswith('    - Timeout:'):
                    settings['timeout'] = int(setting_line.split(':', 1)[1].strip())
                elif setting_line.strip().startswith('    - MaxRetries:'):
                    settings['max_retries'] = int(setting_line.split(':', 1)[1].strip())
                elif setting_line.strip().startswith('    - RetryDelay:'):
                    settings['retry_delay'] = int(setting_line.split(':', 1)[1].strip())
            if settings:
                crew['task_execution_settings'].update(settings)
    
    # Validate crew configuration
    if not crew['name']:
        raise ValueError("Crew name is required")
    if not crew['agents']:
        raise ValueError("At least one agent must be specified in the crew")
    if not crew['tasks']:
        raise ValueError("At least one task must be specified in the crew")
    
    return crew

def _parse_tools_section(section: str) -> List[Dict[str, Any]]:
    """Parse the Tools section into a list of tool configurations."""
    tools = []
    current_tool = None
    
    for line in section.split('\n'):
        line = line.strip()
        if line and line.startswith('- '):
            if current_tool:
                tools.append(current_tool)
            tool_name = line.strip('- ').split(':', 1)[0].strip()
            current_tool = {
                'name': tool_name,
                'description': '',
                'parameters': []
            }
        elif line.startswith('    Description:') and current_tool:
            current_tool['description'] = line.split(':', 1)[1].strip()
        elif line.startswith('    Parameters:'):
            continue  # Skip the header
        elif line.startswith('      - ') and current_tool:
            param_line = line.strip('- ').strip()
            param_name = param_line.split(':', 1)[0].strip()
            param_desc = param_line.split(':', 1)[1].strip()
            
            # Extract type information
            param_type = 'string'  # default
            if '(' in param_name and ')' in param_name:
                type_start = param_name.find('(')
                type_end = param_name.find(')')
                param_type = param_name[type_start+1:type_end].strip()
                param_name = param_name[:type_start].strip()
            
            current_tool['parameters'].append({
                'name': param_name,
                'type': param_type,
                'description': param_desc
            })
    
    if current_tool:
        tools.append(current_tool)
    
    return tools

# def generate_implementation(spec: CrewAISpec, output_dir: Optional[str] = None) -> Dict[str, str]:
#     """
#     Generate a complete CrewAI implementation from specification.
    
#     Args:
#         spec: CrewAISpec object containing the complete specification
#         output_dir: Optional directory to write generated files to
        
#     Returns:
#         Dictionary mapping file names to their generated content
#     """
#     print(f"Generating implementation for {spec.name}")
#     print(f"Spec: {spec}")
#     implementation = {}
    
#     # Generate main implementation file
#     main_code = f'''"""
# {spec.name}

# {spec.description}
# """

# from crewai import Agent, Task, Crew, LLM
# from crewai.tools import Tool
# from typing import Dict, List
# import os
# from dotenv import load_dotenv

# # Load environment variables
# load_dotenv()

# class {spec.name.replace(' ', '')}Crew_creation:
#     """
#     {spec.description}
    
#     This crew is designed to {spec.instructions if spec.instructions else 'work collaboratively on assigned tasks'}.
#     """
    
#     def __init__(self):
#         # Initialize OpenAI LLM
#         self.llm =  LLM(
#             model="azure/development-proactive-ai",
#             base_url=os.environ["AZURE_OPENAI_ENDPOINT"],
#             api_key=os.environ["AZURE_OPENAI_API_KEY"]
#         )
        
#         # Set up tools
#         self.tools = self._create_tools()
#         # Set up agents
#         self.agents = self._create_agents()
#         # Set up tasks
#         self.tasks = self._create_tasks()
#         # Set up crew
#         self.crew = self._create_crew()
    
#     def _create_tools(self) -> Dict[str, Tool]:
#         """Create and configure tools."""
#         tools = {{}}\n'''

#     # Add tools
#     for tool in spec.tools:
#         param_docs = []
#         for param in tool.parameters:
#             param_docs.append(f"            - {param.name} ({param.type}): {param.description}")
        
#         param_docs_str = "\n".join(param_docs) if param_docs else "            None"
        
#         main_code += f'''
#         # {tool.name} tool
#         tools["{tool.name}"] = Tool(
#             name="{tool.name}",
#             description="""{tool.description}
            
#             Parameters:
# {param_docs_str}
#             """,
#             func=lambda **kwargs: print(f"Tool {tool.name} called with args: {{kwargs}}")
#         )\n'''
    
#     main_code += '''
#         return tools
    
#     def _create_agents(self) -> Dict[str, Agent]:
#         """Create and configure agents."""
#         agents = {}\n'''

#     # Add agents
#     for agent in spec.agents:
#         tools_list = [f'self.tools["{t.name}"]' for t in agent.tools]
#         tools_str = ", ".join(tools_list)
#         main_code += f'''
#         # {agent.name} agent
#         agents["{agent.name}"] = Agent(
#             role="An expert {agent.name}",
#             goal="""Serve as {agent.goal} and accomplish assigned tasks effectively.""",
#             backstory="{agent.backstory or f'An expert {agent.name} with extensive experience'}",
#             tools=[{tools_str}],
#             llm=self.llm,
#             verbose=True
#         )\n'''
    
#     main_code += '''
#         return agents
    
#     def _create_tasks(self) -> List[Task]:
#         """Create and configure tasks."""
#         tasks = []
        
#         # Create a mapping of task names to their indices for dependency tracking
#         task_index_map = {task.name: idx for idx, task in enumerate(spec.tasks)}
        
#         # Create all tasks
#         for task in spec.tasks:
#             # Get the agent for this task
#             agent_name = task.agent
            
#             # Validate agent exists
#             if agent_name not in self.agents:
#                 raise ValueError(f"Agent '{agent_name}' specified for task '{task.name}' does not exist")
            
#             # Get dependencies
#             deps = []
#             if hasattr(task, 'dependencies') and task.dependencies:
#                 deps = [d.task_name for d in task.dependencies]
#                 # Validate dependencies exist
#                 for dep in deps:
#                     if dep not in task_index_map:
#                         raise ValueError(f"Dependency '{dep}' for task '{task.name}' does not exist")
            
#             # Get task-specific tools if specified
#             task_tools = []
#             if hasattr(task, 'tools') and task.tools:
#                 for tool_name in task.tools:
#                     if tool_name in self.tools:
#                         task_tools.append(self.tools[tool_name])
            
#             # Create dependency string for task description
#             deps_str = f"Previous tasks: {', '.join(deps)}" if deps else "No dependencies"
            
#             # Create the task with proper configuration
#             tasks.append(Task(
#                 description=f"""{task.description}
                
#                 {deps_str}
#                 Expected Output: {task.expected_output if hasattr(task, 'expected_output') and task.expected_output else 'Complete the assigned task effectively'}
#                 """,
#                 agent=self.agents[agent_name],
#                 context=task.context if hasattr(task, 'context') else None,
#                 tools=task_tools if task_tools else None,
#                 async_execution=task.async_execution if hasattr(task, 'async_execution') else False
#             ))
        
#         return tasks
    
#     def _create_crew(self) -> Crew:
#         """Create and configure the crew."""
#         # Get process type with default to sequential
#         process = spec.crew.process if hasattr(spec.crew, 'process') else "sequential"
        
#         # Get verbosity setting with default to True
#         verbose = spec.crew.verbose if hasattr(spec.crew, 'verbose') else True
        
#         # Create the crew with proper configuration
#         crew = Crew(
#             agents=list(self.agents.values()),
#             tasks=self.tasks,
#             process=process,
#             verbose=verbose
#         )
        
#         # Set any additional crew settings if available
#         if hasattr(spec.crew, 'task_execution_settings'):
#             settings = spec.crew.task_execution_settings
#             if hasattr(settings, 'timeout'):
#                 crew.task_timeout = settings.timeout
#             if hasattr(settings, 'max_retries'):
#                 crew.max_retries = settings.max_retries
#             if hasattr(settings, 'retry_delay'):
#                 crew.retry_delay = settings.retry_delay
        
#         return crew
    
#     def run(self):
#         """Execute the crew's tasks."""
#         return self.crew.kickoff()
# '''
    
#     implementation['generated_agent.py'] = main_code
    
# #     # Generate requirements.txt with specific versions
# #     requirements = '''# Core dependencies
# # crewai==0.11.1
# # langchain==0.1.0
# # langchain-openai==0.0.2.post1
# # openai==1.3.0

# # # Environment and utilities
# # python-dotenv==1.0.0
# # duckduckgo-search==4.1.1

# # # Additional tools
# # requests==2.31.0
# # beautifulsoup4==4.12.2
# # pandas==2.1.3
# # numpy==1.24.3'''
    
# #     implementation['requirements.txt'] = requirements
    
# #     # Generate .env.example
# #     env_example = '''# OpenAI API Key (Required)
# # OPENAI_API_KEY=your-api-key-here

# # # Optional API Keys
# # SERPAPI_API_KEY=your-serpapi-key-here
# # BROWSERLESS_API_KEY=your-browserless-key-here

# # # Configuration
# # OPENAI_MODEL=gpt-4  # or gpt-3.5-turbo
# # TEMPERATURE=0.7     # 0.0 to 1.0'''
    
# #     implementation['.env.example'] = env_example
    
# #     # Generate README.md
# #     readme = f'''# {spec.name}

# # {spec.description}

# # ## Overview

# # {spec.instructions}

# # ## Installation

# # 1. Clone this repository
# # 2. Install dependencies:
# #    ```bash
# #    pip install -r requirements.txt
# #    ```
# # 3. Set up environment variables:
# #    ```bash
# #    cp .env.example .env
# #    # Edit .env with your API keys
# #    ```

# # ## Components

# # ### Agents

# # {chr(10).join(f"- **{agent.name}** ({agent.role.title})" for agent in spec.agents)}

# # ### Tasks

# # {chr(10).join(f"- **{task.name}**: {task.description}" for task in spec.tasks)}

# # ### Tools

# # {chr(10).join(f"- **{tool.name}**: {tool.description}" for tool in spec.tools)}

# # ## Usage

# # ```python
# # from generated_agent import {spec.name.replace(' ', '')}Crew

# # # Create and run the crew
# # crew = {spec.name.replace(' ', '')}Crew()
# # result = crew.run()
# # print(result)
# # ```

# # ## Configuration

# # You can modify the following in `.env`:
# # - `OPENAI_API_KEY`: Your OpenAI API key
# # - `OPENAI_MODEL`: The model to use (default: gpt-4)
# # - `TEMPERATURE`: Model temperature (default: 0.7)

# # ## License

# # MIT'''
    
# #     implementation['README.md'] = readme
    
#     # Write files if output directory provided
#     if output_dir:
#         output_path = Path(output_dir)
#         output_path.mkdir(parents=True, exist_ok=True)
        
#         for filename, content in implementation.items():
#             file_path = output_path / filename
#             with open(file_path, 'w') as f:
#                 f.write(content)
    
#     return implementation 

def generate_implementation(spec: CrewAISpec, output_dir: Optional[str] = None) -> Dict[str, str]:
    """
    Generate a complete CrewAI implementation from specification.
    
    Args:
        spec: CrewAISpec object containing the complete specification
        output_dir: Optional directory to write generated files to
        
    Returns:
        Dictionary mapping file names to their generated content
    """
    print(f"Generating implementation for {spec.name}")
    print(f"Spec: {spec}")
    implementation = {}
    
    # Generate main implementation file
    main_code = f'''"""
{spec.name}

{spec.description}
"""

from crewai import Agent, Task, Crew, LLM, Process
from crewai.tools import Tool
from typing import Dict, List
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class {spec.name.replace(' ', '')}Crew:
    """
    {spec.description}
    
    This crew is designed to {spec.instructions if spec.instructions else 'work collaboratively on assigned tasks'}.
    """
    
    def __init__(self):
        # Initialize OpenAI LLM
        self.llm =  LLM(
            model="azure/development-proactive-ai",
            base_url=os.environ["AZURE_OPENAI_ENDPOINT"],
            api_key=os.environ["AZURE_OPENAI_API_KEY"]
        )
        
        # Set up tools
        self.tools = self._create_tools()
        # Set up agents
        self.agents = self._create_agents()
        # Set up tasks
        self.tasks = self._create_tasks()
        # Set up crew
        self.crew = self._create_crew()
    
    def _create_tools(self) -> Dict[str, Tool]:
        """Create and configure tools."""
        tools = {{}}\n'''

    # Add tools
    for tool in spec.tools:
        param_docs = []
        for param in tool.parameters:
            param_docs.append(f"            - {param.name} ({param.type}): {param.description}")
        
        param_docs_str = "\n".join(param_docs) if param_docs else "            None"
        
        main_code += f'''
        # {tool.name} tool
        tools["{tool.name}"] = Tool(
            name="{tool.name}",
            description="""{tool.description}
            
            Parameters:
{param_docs_str}
            """,
            func=lambda **kwargs: print(f"Tool {tool.name} called with args: {{kwargs}}")
        )\n'''
    
    main_code += '''
        return tools
    
    def _create_agents(self) -> Dict[str, Agent]:
        """Create and configure agents."""
        agents = {}\n'''

    # Add agents
    for agent in spec.agents:
        # Get the only the tool names from the tools list
        # Extract just the tool names from ToolConfig objects
        agent_tools = []
        if hasattr(agent, 'tools') and agent.tools:
            for tool_config in agent.tools:
                # Handle both string and ToolConfig cases
                if isinstance(tool_config, str):
                    agent_tools.append(tool_config)
                else:
                    # Assuming tool_config has a name attribute
                    agent_tools.append(tool_config.name)

        goal_str = agent.goal
            
        main_code += f'''
        # {agent.name} agent
        agents["{agent.name}"] = Agent(
            role="An expert {agent.name}",
            goal="""Serve as {goal_str} and accomplish assigned tasks effectively.""",
            backstory="{agent.backstory if hasattr(agent, 'backstory') and agent.backstory else f'An expert {agent.name} with extensive experience'}",
            tools={agent_tools},
            llm=self.llm,
            verbose=True
        )\n'''
    
    main_code += '''
        return agents
    
    def _create_tasks(self) -> List[Task]:
        """Create and configure tasks."""
        tasks = {}\n'''

    # Add tasks
    for task in spec.tasks:
        # Get task-specific tools if specified
        task_tools = []
        if hasattr(task, 'tools') and task.tools:
            task_tools = [t for t in task.tools if t in [tool.name for tool in spec.tools]]
            
        task_tools_str = ", ".join(task_tools)
        
        # Get context with default empty string
        context = task.context if hasattr(task, 'context') and task.context else ""
        
        # Get expected output
        expected_output = task.expected_output if hasattr(task, 'expected_output') else "Complete the task effectively"
        
        # Get async execution setting
        async_execution = task.async_execution if hasattr(task, 'async_execution') else False
        
        dependencies = []
        if hasattr(task, 'dependencies') and task.dependencies:
            dependencies = [d.task_name for d in task.dependencies]
        
        main_code += f'''
        tasks["{task.name}"] = Task(
            description="{task.description}",
            expected_output="{expected_output}",
            agent=self.agents["{task.agent}"],
            tools=[{task_tools_str}],
            context="{context}",
            async_execution={str(async_execution)},
            dependencies={dependencies}
        )\n'''
        


    main_code += '''
        return list(tasks.values())
    
    def _create_crew(self) -> Crew:
        """Create and configure the crew."""\n'''
    # Get process type with default to sequential
    process = spec.crew.process if hasattr(spec.crew, 'process') else "sequential"
        
    # Get verbosity setting with default to True
    verbose = spec.crew.verbose if hasattr(spec.crew, 'verbose') else True
        
    # Get memory setting if available
    memory = spec.crew.memory if hasattr(spec.crew, 'memory') else False
        
    # Get task execution settings with defaults
    task_timeout = None
    max_retries = None
    retry_delay = None
        
    if hasattr(spec.crew, 'task_execution_settings'):
        settings = spec.crew.task_execution_settings
        task_timeout = settings.timeout if hasattr(settings, 'timeout') else None
        max_retries = settings.max_retries if hasattr(settings, 'max_retries') else None
        retry_delay = settings.retry_delay if hasattr(settings, 'retry_delay') else None
        
    main_code += f'''
        # Create the crew with all available configuration options
        crew = Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.{process},
            verbose={verbose},
            memory={memory},
            task_timeout={task_timeout},
            max_retries={max_retries},
            retry_delay={retry_delay}
        )

        
        return crew
    
    def run(self):
        """Execute the crew's tasks."""
        return self.crew.kickoff()

def main():
    crew = {spec.name.replace(' ', '')}Crew()
    result = crew.run()
    print(result)

if __name__ == "__main__":
    main()
'''
    
    implementation['generated_agent.py'] = main_code
    
    # Write files if output directory provided
    if output_dir:
        from pathlib import Path
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for filename, content in implementation.items():
            file_path = output_path / filename
            with open(file_path, 'w') as f:
                f.write(content)
    
    return implementation