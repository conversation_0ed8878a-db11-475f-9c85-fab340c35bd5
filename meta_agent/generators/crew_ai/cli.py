"""
Command-line interface for the CrewAI Generator.
"""

import sys
import argparse
from typing import Optional
from pathlib import Path

from .main_generator import parse_spec_file, generate_implementation

def main(args: Optional[list] = None):
    """
    Main entry point for the CrewAI Generator CLI.
    
    Args:
        args: Optional list of command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Generate CrewAI agents from specifications"
    )
    parser.add_argument(
        "spec_file",
        nargs="?",
        type=str,
        help="Path to the specification file (JSON or text)"
    )
    parser.add_argument(
        "output_file",
        nargs="?",
        type=str,
        default="generated_agent.py",
        help="Output file path (default: generated_agent.py)"
    )
    
    args = parser.parse_args(args)
    
    # If no spec file provided, read from stdin
    if not args.spec_file:
        print("Enter your agent specification (press Ctrl+D when done):")
        spec_content = sys.stdin.read()
        # Create a temporary file
        temp_file = Path("temp_spec.txt")
        temp_file.write_text(spec_content)
        spec_file = str(temp_file)
    else:
        spec_file = args.spec_file
    
    try:
        # Parse specification
        spec = parse_spec_file(spec_file)
        
        # Generate implementation
        output_dir = str(Path(args.output_file).parent)
        implementation = generate_implementation(spec, output_dir)
        
        print(f"\nGenerated files in {output_dir}:")
        for filename in implementation:
            print(f"- {filename}")
        
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)
    finally:
        # Clean up temporary file if it exists
        if not args.spec_file:
            temp_file.unlink(missing_ok=True)

if __name__ == "__main__":
    main() 