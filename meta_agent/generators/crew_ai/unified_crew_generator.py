import logging
from typing import Optional, Dict, Any, List, Union, Type
from crewai import Crew, Process
from pydantic import BaseModel, Field, validator
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CrewConfig(BaseModel):
    """Configuration model for CrewAI crews with comprehensive validation."""
    
    # Required parameters
    agents: List[Any] = Field(..., description="List of agents that are part of the crew")
    tasks: List[Any] = Field(..., description="List of tasks assigned to the crew")
    
    # Optional parameters with defaults
    process: Process = Field(default=Process.sequential, description="The process flow the crew follows")
    verbose: bool = Field(default=False, description="Verbosity level for logging during execution")
    
    # Advanced configuration
    manager_llm: Optional[Any] = Field(default=None, description="LLM used by the manager agent in hierarchical process")
    function_calling_llm: Optional[Any] = Field(default=None, description="LLM used for function calling for all agents")
    config: Optional[Dict[str, Any]] = Field(default=None, description="Optional configuration settings for the crew")
    max_rpm: Optional[int] = Field(default=None, description="Maximum requests per minute during execution")
    
    # Memory and Cache
    memory: bool = Field(default=False, description="Enable memory for storing execution memories")
    memory_config: Optional[Dict[str, Any]] = Field(default=None, description="Configuration for the memory provider")
    cache: bool = Field(default=True, description="Use cache for storing tool execution results")
    embedder: Optional[Dict[str, Any]] = Field(
        default={"provider": "openai"}, 
        description="Configuration for the embedder"
    )
    
    # Callbacks
    step_callback: Optional[Any] = Field(
        default=None, 
        description="Function called after each step of every agent"
    )
    task_callback: Optional[Any] = Field(
        default=None, 
        description="Function called after completion of each task"
    )
    
    # Additional settings
    share_crew: bool = Field(
        default=False, 
        description="Share crew information with crewAI team"
    )
    output_log_file: Optional[Union[bool, str]] = Field(
        default=None, 
        description="Save logs to file (True for logs.txt, or specify filename)"
    )
    manager_agent: Optional[Any] = Field(
        default=None, 
        description="Custom agent to be used as manager"
    )
    prompt_file: Optional[str] = Field(
        default=None, 
        description="Path to prompt JSON file"
    )
    
    # Planning
    planning: bool = Field(
        default=False, 
        description="Enable planning ability for the crew"
    )
    planning_llm: Optional[Any] = Field(
        default=None, 
        description="LLM used by the AgentPlanner"
    )
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('agents')
    def validate_agents(cls, v):
        """Validate that agents are properly configured."""
        if not v:
            raise ValueError("At least one agent is required")
        return v
    
    @validator('tasks')
    def validate_tasks(cls, v):
        """Validate that tasks are properly configured."""
        if not v:
            raise ValueError("At least one task is required")
        return v
    
    @validator('process')
    def validate_process(cls, v, values):
        """Validate process configuration."""
        if v == Process.hierarchical and not (values.get('manager_llm') or values.get('manager_agent')):
            raise ValueError("manager_llm or manager_agent is required for hierarchical process")
        return v
    
    @validator('output_log_file')
    def validate_output_log_file(cls, v):
        """Validate output log file configuration."""
        if isinstance(v, str) and not v.endswith(('.txt', '.json')):
            raise ValueError("Output log file must end with .txt or .json")
        return v

def create_crew(config: Union[Dict[str, Any], CrewConfig]) -> Optional[Crew]:
    """
    Create a CrewAI crew with comprehensive configuration and validation.
    
    Args:
        config: Either a dictionary or CrewConfig object containing crew parameters
        
    Returns:
        Optional[Crew]: Created CrewAI crew or None if creation fails
        
    Raises:
        ValueError: If required parameters are missing or invalid
    """
    try:
        # Convert dict to CrewConfig if needed
        if isinstance(config, dict):
            config = CrewConfig(**config)
        
        # Create crew with all available attributes
        crew = Crew(
            agents=config.agents,
            tasks=config.tasks,
            process=config.process,
            verbose=config.verbose,
            manager_llm=config.manager_llm,
            function_calling_llm=config.function_calling_llm,
            config=config.config,
            max_rpm=config.max_rpm,
            memory=config.memory,
            memory_config=config.memory_config,
            cache=config.cache,
            embedder=config.embedder,
            step_callback=config.step_callback,
            task_callback=config.task_callback,
            share_crew=config.share_crew,
            output_log_file=config.output_log_file,
            manager_agent=config.manager_agent,
            prompt_file=config.prompt_file,
            planning=config.planning,
            planning_llm=config.planning_llm
        )
        
        logger.info("Successfully created crew")
        return crew
        
    except Exception as e:
        logger.error(f"Error creating crew: {str(e)}")
        return None

def create_crew_from_yaml(yaml_path: str) -> Optional[Crew]:
    """
    Create a CrewAI crew from a YAML configuration file.
    
    Args:
        yaml_path: Path to the YAML configuration file
        
    Returns:
        Optional[Crew]: Created CrewAI crew or None if creation fails
    """
    try:
        with open(yaml_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        return create_crew(config_dict)
    except Exception as e:
        logger.error(f"Error creating crew from YAML: {str(e)}")
        return None

def create_crew_with_planning(
    agents: List[Any],
    tasks: List[Any],
    planning_llm: Any,
    **kwargs
) -> Optional[Crew]:
    """
    Create a CrewAI crew with planning enabled.
    
    Args:
        agents: List of agents
        tasks: List of tasks
        planning_llm: LLM to be used by the AgentPlanner
        **kwargs: Additional crew configuration parameters
        
    Returns:
        Optional[Crew]: Created CrewAI crew or None if creation fails
    """
    config = {
        "agents": agents,
        "tasks": tasks,
        "planning": True,
        "planning_llm": planning_llm,
        **kwargs
    }
    return create_crew(config)

def create_hierarchical_crew(
    agents: List[Any],
    tasks: List[Any],
    manager_llm: Any,
    **kwargs
) -> Optional[Crew]:
    """
    Create a CrewAI crew with hierarchical process.
    
    Args:
        agents: List of agents
        tasks: List of tasks
        manager_llm: LLM to be used by the manager agent
        **kwargs: Additional crew configuration parameters
        
    Returns:
        Optional[Crew]: Created CrewAI crew or None if creation fails
    """
    config = {
        "agents": agents,
        "tasks": tasks,
        "process": Process.hierarchical,
        "manager_llm": manager_llm,
        **kwargs
    }
    return create_crew(config) 