"""
Tool Generator for CrewAI - Creates custom tools based on specifications.

This module follows the CrewAI documentation for creating tools:
https://docs.crewai.com/how-to/create-custom-tools/
"""

from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

@dataclass
class ToolConfig:
    name: str
    description: str
    func: Callable
    parameters: Optional[Dict[str, Any]] = None

def create_basic_tool(spec: Dict[str, Any]) -> str:
    """
    Generate code for a basic CrewAI tool.
    
    Args:
        spec: Tool specification containing name, description, and parameters
        
    Returns:
        String containing the generated tool code
    """
    name = spec['name'].lower().replace(' ', '_')
    description = spec.get('description', f"Tool for {spec['name']}")
    parameters = spec.get('parameters', {})
    
    # Generate parameter handling code
    param_code = ""
    if parameters:
        param_list = []
        for param_name, param_info in parameters.items():
            param_type = param_info.get('type', 'str')
            required = param_info.get('required', True)
            param_list.append(f"{param_name}: {param_type}")
        param_code = ", ".join(param_list)
    
    code = f"""from crewai import Tool
from typing import Optional

def {name}_function({param_code}):
    \"\"\"
    {description}
    \"\"\"
    # TODO: Implement the actual tool functionality
    pass

{name}_tool = Tool(
    name="{spec['name']}",
    description="{description}",
    func={name}_function
)
"""
    return code

def create_api_tool(
    name: str,
    description: str,
    api_url: str,
    method: str = "GET",
    headers: Optional[Dict[str, str]] = None
) -> str:
    """
    Generate code for a tool that makes API calls.
    
    Args:
        name: Tool name
        description: Tool description
        api_url: API endpoint URL
        method: HTTP method (default: GET)
        headers: Optional headers for API calls
        
    Returns:
        String containing the generated API tool code
    """
    tool_name = name.lower().replace(' ', '_')
    
    headers_code = ""
    if headers:
        headers_str = ", ".join([f"'{k}': '{v}'" for k, v in headers.items()])
        headers_code = f"    headers = {{{headers_str}}}\n"
    
    code = f"""from crewai import Tool
import requests
from typing import Optional

def {tool_name}_function(**kwargs):
    \"\"\"
    {description}
    \"\"\"
    url = "{api_url}"
{headers_code}    
    try:
        response = requests.{method.lower()}(
            url,
            {'headers=headers, ' if headers else ''}**kwargs
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return f"Error: {{str(e)}}"

{tool_name}_tool = Tool(
    name="{name}",
    description="{description}",
    func={tool_name}_function
)
"""
    return code

def create_search_tool(name: str, search_type: str = "web") -> str:
    """
    Generate code for a search tool using DuckDuckGo or similar services.
    
    Args:
        name: Tool name
        search_type: Type of search (web, news, etc.)
        
    Returns:
        String containing the generated search tool code
    """
    tool_name = name.lower().replace(' ', '_')
    
    code = f"""from crewai import Tool
from duckduckgo_search import DDGS

def {tool_name}_function(query: str, max_results: int = 5):
    \"\"\"
    Perform a {search_type} search using DuckDuckGo.
    \"\"\"
    try:
        with DDGS() as ddgs:
            results = [r for r in ddgs.{search_type}(
                query,
                max_results=max_results
            )]
        return results
    except Exception as e:
        return f"Error performing search: {{str(e)}}"

{tool_name}_tool = Tool(
    name="{name}",
    description=f"Search {search_type} using DuckDuckGo",
    func={tool_name}_function
)
"""
    return code

def create_file_tool(name: str, operation: str = "read") -> str:
    """
    Generate code for a tool that handles file operations.
    
    Args:
        name: Tool name
        operation: File operation (read, write, append)
        
    Returns:
        String containing the generated file tool code
    """
    tool_name = name.lower().replace(' ', '_')
    
    operations = {
        "read": """
    with open(file_path, 'r') as f:
        return f.read()""",
        "write": """
    with open(file_path, 'w') as f:
        f.write(content)
    return f"Successfully wrote to {file_path}\"""",
        "append": """
    with open(file_path, 'a') as f:
        f.write(content)
    return f"Successfully appended to {file_path}\""""
    }
    
    operation_code = operations.get(operation, operations["read"])
    
    code = f"""from crewai import Tool
from typing import Optional

def {tool_name}_function(file_path: str, content: Optional[str] = None):
    \"\"\"
    {operation.capitalize()} content {'from' if operation == 'read' else 'to'} a file.
    \"\"\"
    try:{operation_code}
    except Exception as e:
        return f"Error: {{str(e)}}"

{tool_name}_tool = Tool(
    name="{name}",
    description=f"{operation.capitalize()} content {'from' if operation == 'read' else 'to'} a file",
    func={tool_name}_function
)
"""
    return code 