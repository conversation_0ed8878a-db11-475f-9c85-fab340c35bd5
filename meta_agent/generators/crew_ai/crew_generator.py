"""
Crew Generator for CrewAI - Creates crews based on specifications.

This module follows the CrewAI documentation for creating crews:
https://docs.crewai.com/concepts/crews/
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass

@dataclass
class CrewConfig:
    agents: List[str]
    tasks: List[str]
    process: Optional[str] = "sequential"  # or "hierarchical"
    verbose: bool = True

def create_crew(spec: Dict[str, Any]) -> str:
    """
    Generate CrewAI crew code from specification.
    
    Args:
        spec: Crew specification containing agents and tasks
        
    Returns:
        String containing the generated crew code
    """
    name = spec['name'].lower().replace(' ', '_')
    agents = spec.get('agents', [])
    tasks = spec.get('tasks', [])
    
    agents_list = ", ".join(agents)
    tasks_list = ", ".join(tasks)
    
    code = f"""from crewai import Crew

{name} = Crew(
    agents=[{agents_list}],
    tasks=[{tasks_list}],
    verbose=True
)

# Execute the crew's tasks
results = {name}.kickoff()
"""
    return code

def create_sequential_crew(
    name: str,
    agents: List[str],
    tasks: List[str],
    verbose: bool = True
) -> str:
    """
    Generate code for a crew that executes tasks sequentially.
    
    Args:
        name: Crew name
        agents: List of agent names
        tasks: List of task names
        verbose: Whether to enable verbose output
        
    Returns:
        String containing the generated sequential crew code
    """
    crew_name = name.lower().replace(' ', '_')
    agents_list = ", ".join(agents)
    tasks_list = ", ".join(tasks)
    
    code = f"""from crewai import Crew, Process

{crew_name} = Crew(
    agents=[{agents_list}],
    tasks=[{tasks_list}],
    verbose={str(verbose)},
    process=Process.sequential
)

# Execute tasks sequentially
results = {crew_name}.kickoff()
"""
    return code

def create_hierarchical_crew(
    name: str,
    manager_agent: str,
    worker_agents: List[str],
    tasks: List[str],
    all_agents: Optional[List[str]] = None
) -> str:
    """
    Generate code for a hierarchical crew with a manager and workers.
    
    Args:
        name: Crew name
        manager_agent: Name of the managing agent
        worker_agents: List of worker agent names
        tasks: List of task names
        all_agents: Optional list of all available agents for validation
        
    Returns:
        String containing the generated hierarchical crew code
        
    Raises:
        ValueError: If manager or worker agents are not found in all_agents list
    """
    # Validate agents if all_agents list is provided
    if all_agents is not None:
        if manager_agent not in all_agents:
            raise ValueError(f"Manager agent '{manager_agent}' not found in available agents")
        
        invalid_workers = [w for w in worker_agents if w not in all_agents]
        if invalid_workers:
            raise ValueError(f"Worker agent(s) not found in available agents: {', '.join(invalid_workers)}")
        
        if manager_agent in worker_agents:
            raise ValueError(f"Manager agent '{manager_agent}' cannot also be a worker")

    crew_name = name.lower().replace(' ', '_')
    workers_list = ", ".join(worker_agents)
    tasks_list = ", ".join(tasks)
    
    code = f"""from crewai import Crew, Process

# Create the hierarchical crew with validated manager-worker structure
{crew_name} = Crew(
    agents=[{manager_agent}, {workers_list}],
    tasks=[{tasks_list}],
    verbose=True,
    process=Process.hierarchical,
    manager_id="{manager_agent}",  # Specify the manager agent
    delegation_rules={{
        "task_assignment": "skills",  # Assign tasks based on agent skills
        "workload_distribution": "balanced"  # Ensure balanced workload among workers
    }}
)

# Execute tasks with hierarchical management
results = {crew_name}.kickoff()
"""
    return code

def create_custom_process_crew(
    name: str,
    agents: List[str],
    tasks: List[str],
    process_config: Dict[str, Any]
) -> str:
    """
    Generate code for a crew with custom task execution process.
    
    Args:
        name: Crew name
        agents: List of agent names
        tasks: List of task names
        process_config: Configuration for custom process
        
    Returns:
        String containing the generated custom crew code
    """
    crew_name = name.lower().replace(' ', '_')
    agents_list = ", ".join(agents)
    tasks_list = ", ".join(tasks)
    
    # Convert process config to code
    process_code = []
    for key, value in process_config.items():
        if isinstance(value, str):
            process_code.append(f'    "{key}": "{value}"')
        else:
            process_code.append(f'    "{key}": {value}')
    
    process_config_str = ",\n".join(process_code)
    
    code = f"""from crewai import Crew, Process

# Custom process configuration
process_config = {{
{process_config_str}
}}

# Create crew with custom process
{crew_name} = Crew(
    agents=[{agents_list}],
    tasks=[{tasks_list}],
    verbose=True,
    process=Process.custom,
    process_config={process_config_str}
)

# Execute tasks with custom process
results = {crew_name}.kickoff()
"""
    return code 