"""
Crew Orchestrator for CrewAI - Coordinates the creation of agents, tasks, and crews.

This module provides a unified interface for generating complete CrewAI implementations
from specifications in both JSON and YAML formats.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import yaml
import json

from .unified_agent_generator import create_crew_agent, create_agent_from_dict
from .unified_task_generator import create_task, create_sequential_tasks
from .unified_crew_generator import create_crew, create_crew_from_yaml

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CrewAISpec:
    """Specification model for complete CrewAI implementation."""
    
    def __init__(
        self,
        name: str,
        description: str,
        agents: List[Dict[str, Any]],
        tasks: List[Dict[str, Any]],
        crew_config: Dict[str, Any],
        tools: Optional[List[Dict[str, Any]]] = None
    ):
        self.name = name
        self.description = description
        self.agents = agents
        self.tasks = tasks
        self.crew_config = crew_config
        self.tools = tools or []

def parse_spec_file(spec_file: Union[str, Path]) -> CrewAISpec:
    """
    Parse a specification file into a CrewAISpec object.
    Supports both JSON and YAML formats.
    
    Args:
        spec_file: Path to the specification file
        
    Returns:
        CrewAISpec object containing the parsed specification
        
    Raises:
        ValueError: If the file format is invalid or required sections are missing
    """
    try:
        with open(spec_file, 'r') as f:
            if str(spec_file).endswith('.json'):
                spec_dict = json.load(f)
            else:
                spec_dict = yaml.safe_load(f)
        
        # Validate required sections
        required_sections = ['name', 'description', 'agents', 'tasks', 'crew']
        missing_sections = [s for s in required_sections if s not in spec_dict]
        if missing_sections:
            raise ValueError(f"Missing required sections: {', '.join(missing_sections)}")
        
        return CrewAISpec(
            name=spec_dict['name'],
            description=spec_dict['description'],
            agents=spec_dict['agents'],
            tasks=spec_dict['tasks'],
            crew_config=spec_dict['crew'],
            tools=spec_dict.get('tools', [])
        )
    except Exception as e:
        logger.error(f"Error parsing specification file: {str(e)}")
        raise

def generate_implementation(spec: CrewAISpec, output_dir: Optional[str] = None) -> Dict[str, str]:
    """
    Generate a complete CrewAI implementation from specification.
    
    Args:
        spec: CrewAISpec object containing the complete specification
        output_dir: Optional directory to write generated files to
        
    Returns:
        Dictionary mapping file names to their generated content
    """
    try:
        # Create agents
        agents = {}
        for agent_config in spec.agents:
            agent = create_agent_from_dict(agent_config)
            if agent:
                agents[agent_config['name']] = agent
            else:
                logger.warning(f"Failed to create agent: {agent_config['name']}")
        
        # Create tasks
        tasks = []
        for task_config in spec.tasks:
            task = create_task(task_config)
            if task:
                tasks.append(task)
            else:
                logger.warning(f"Failed to create task: {task_config.get('name', 'Unnamed Task')}")
        
        # Create crew
        crew_config = spec.crew_config
        crew_config['agents'] = list(agents.values())
        crew_config['tasks'] = tasks
        crew = create_crew(crew_config)
        
        if not crew:
            raise ValueError("Failed to create crew")
        
        # Generate implementation code
        implementation = {}
        
        # Generate main implementation file
        main_code = f'''"""
{spec.name}

{spec.description}
"""

from crewai import Agent, Task, Crew, Process
from typing import Dict, List
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class {spec.name.replace(' ', '')}Crew:
    """
    {spec.description}
    """
    
    def __init__(self):
        # Set up agents
        self.agents = self._create_agents()
        # Set up tasks
        self.tasks = self._create_tasks()
        # Set up crew
        self.crew = self._create_crew()
    
    def _create_agents(self) -> Dict[str, Agent]:
        """Create and configure agents."""
        agents = {{}}\n'''

        # Add agents
        for agent_config in spec.agents:
            main_code += f'''
        # {agent_config['name']} agent
        agents["{agent_config['name']}"] = Agent(
            role="{agent_config['role']}",
            goal="{agent_config['goal']}",
            backstory="{agent_config.get('backstory', f'An expert {agent_config["role"]} with extensive experience')}",
            tools={agent_config.get('tools', [])},
            verbose={agent_config.get('verbose', True)}
        )\n'''
        
        main_code += '''
        return agents
    
    def _create_tasks(self) -> List[Task]:
        """Create and configure tasks."""
        tasks = []\n'''

        # Add tasks
        for task_config in spec.tasks:
            main_code += f'''
        # {task_config.get('name', 'Unnamed Task')} task
        tasks.append(Task(
            description="{task_config['description']}",
            agent=self.agents["{task_config['agent']}"],
            expected_output="{task_config.get('expected_output', 'Complete the task effectively')}",
            tools={task_config.get('tools', [])},
            context={task_config.get('context', [])},
            async_execution={task_config.get('async_execution', False)}
        ))\n'''
        
        main_code += '''
        return tasks
    
    def _create_crew(self) -> Crew:
        """Create and configure the crew."""
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=Process.{spec.crew_config.get('process', 'sequential')},
            verbose={spec.crew_config.get('verbose', True)},
            memory={spec.crew_config.get('memory', False)},
            cache={spec.crew_config.get('cache', True)}
        )
    
    def run(self):
        """Execute the crew's tasks."""
        return self.crew.kickoff()

def main():
    crew = {spec.name.replace(' ', '')}Crew()
    result = crew.run()
    print(result)

if __name__ == "__main__":
    main()
'''

        implementation['generated_crew.py'] = main_code
        
        # Generate requirements.txt
        requirements = '''# Core dependencies
crewai>=0.11.0
langchain>=0.1.0
langchain-openai>=0.0.2
openai>=1.3.0

# Environment and utilities
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Additional tools
requests>=2.31.0
beautifulsoup4>=4.12.2
pandas>=2.1.3
numpy>=1.24.3'''
        
        implementation['requirements.txt'] = requirements
        
        # Generate .env.example
        env_example = '''# OpenAI API Key (Required)
OPENAI_API_KEY=your-api-key-here

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=your-azure-endpoint-here
AZURE_OPENAI_API_KEY=your-azure-api-key-here

# Optional API Keys
SERPAPI_API_KEY=your-serpapi-key-here
BROWSERLESS_API_KEY=your-browserless-key-here

# Configuration
OPENAI_MODEL=gpt-4  # or gpt-3.5-turbo
TEMPERATURE=0.7     # 0.0 to 1.0'''
        
        implementation['.env.example'] = env_example
        
        # Generate README.md
        readme = f'''# {spec.name}

{spec.description}

## Overview

This project implements a CrewAI crew for {spec.description.lower()}.

## Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

## Components

### Agents

{chr(10).join(f"- **{agent['name']}** ({agent['role']})" for agent in spec.agents)}

### Tasks

{chr(10).join(f"- **{task.get('name', 'Unnamed Task')}**: {task['description']}" for task in spec.tasks)}

### Tools

{chr(10).join(f"- **{tool['name']}**: {tool['description']}" for tool in spec.tools) if spec.tools else "No custom tools defined"}

## Usage

```python
from generated_crew import {spec.name.replace(' ', '')}Crew

# Create and run the crew
crew = {spec.name.replace(' ', '')}Crew()
result = crew.run()
print(result)
```

## Configuration

You can modify the following in `.env`:
- `OPENAI_API_KEY`: Your OpenAI API key
- `AZURE_OPENAI_ENDPOINT`: Your Azure OpenAI endpoint
- `AZURE_OPENAI_API_KEY`: Your Azure OpenAI API key
- `OPENAI_MODEL`: The model to use (default: gpt-4)
- `TEMPERATURE`: Model temperature (default: 0.7)

## License

MIT'''
        
        implementation['README.md'] = readme
        
        # Write files if output directory provided
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            for filename, content in implementation.items():
                file_path = output_path / filename
                with open(file_path, 'w') as f:
                    f.write(content)
        
        return implementation
        
    except Exception as e:
        logger.error(f"Error generating implementation: {str(e)}")
        raise

def generate_from_spec_file(spec_file: Union[str, Path], output_dir: Optional[str] = None) -> Dict[str, str]:
    """
    Generate a complete CrewAI implementation from a specification file.
    
    Args:
        spec_file: Path to the specification file (JSON or YAML)
        output_dir: Optional directory to write generated files to
        
    Returns:
        Dictionary mapping file names to their generated content
    """
    try:
        spec = parse_spec_file(spec_file)
        return generate_implementation(spec, output_dir)
    except Exception as e:
        logger.error(f"Error generating from spec file: {str(e)}")
        raise 