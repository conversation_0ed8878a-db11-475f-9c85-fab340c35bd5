import logging
import os
from typing import Optional, Dict, Any, List, Union, Type, Tuple
from crewai import Task
from pydantic import BaseModel, Field, validator
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskConfig(BaseModel):
    """Configuration model for CrewAI tasks with comprehensive validation."""
    
    # Required parameters
    description: str = Field(..., description="Clear, concise statement of what the task entails")
    agent: str = Field(..., description="Name of the agent responsible for executing the task")
    
    # Optional parameters with defaults
    name: Optional[str] = Field(default=None, description="Name identifier for the task")
    expected_output: Optional[str] = Field(default=None, description="Detailed description of what task completion looks like")
    tools: Optional[List[Any]] = Field(default=[], description="Tools/resources the agent is limited to use")
    context: Optional[List[str]] = Field(default=[], description="Other tasks whose outputs will be used as context")
    
    # Advanced configuration
    async_execution: bool = Field(default=False, description="Whether the task should be executed asynchronously")
    human_input: bool = Field(default=False, description="Whether the task should have human review")
    config: Optional[Dict[str, Any]] = Field(default=None, description="Task-specific configuration parameters")
    
    # Output handling
    output_file: Optional[str] = Field(default=None, description="File path for storing task output")
    output_json: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model to structure JSON output")
    output_pydantic: Optional[Type[BaseModel]] = Field(default=None, description="Pydantic model for task output")
    create_directory: bool = Field(default=False, description="Create directories when saving output file")
    
    # Task guardrails
    guardrail: Optional[Any] = Field(default=None, description="Function to validate/transform task output")
    max_retries: int = Field(default=3, description="Maximum number of retries for task execution")
    
    # Callback
    callback: Optional[Any] = Field(default=None, description="Function to be executed after task completion")
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('output_file')
    def validate_output_file(cls, v, values):
        """Validate output file path and create directory if needed."""
        if v and values.get('create_directory'):
            os.makedirs(os.path.dirname(v), exist_ok=True)
        return v
    
    @validator('context')
    def validate_context(cls, v):
        """Validate that context tasks exist."""
        if v and not all(isinstance(task, str) for task in v):
            raise ValueError("Context must be a list of task names")
        return v
    
    @validator('tools')
    def validate_tools(cls, v):
        """Validate that tools are properly configured."""
        if v and not all(hasattr(tool, 'name') for tool in v):
            raise ValueError("All tools must have a 'name' attribute")
        return v

def create_task(config: Union[Dict[str, Any], TaskConfig]) -> Optional[Task]:
    """
    Create a CrewAI task with comprehensive configuration and validation.
    
    Args:
        config: Either a dictionary or TaskConfig object containing task parameters
        
    Returns:
        Optional[Task]: Created CrewAI task or None if creation fails
        
    Raises:
        ValueError: If required parameters are missing or invalid
    """
    try:
        # Convert dict to TaskConfig if needed
        if isinstance(config, dict):
            config = TaskConfig(**config)
        
        # Validate required parameters
        if not config.description or not config.agent:
            raise ValueError("Description and agent are required parameters")
        
        # Validate output configuration
        if config.output_json and config.output_pydantic:
            raise ValueError("Cannot specify both output_json and output_pydantic")
        
        # Create task with all available attributes
        task = Task(
            description=config.description,
            agent=config.agent,
            name=config.name,
            expected_output=config.expected_output,
            tools=config.tools,
            context=config.context,
            async_execution=config.async_execution,
            human_input=config.human_input,
            config=config.config,
            output_file=config.output_file,
            output_json=config.output_json,
            output_pydantic=config.output_pydantic,
            create_directory=config.create_directory,
            guardrail=config.guardrail,
            max_retries=config.max_retries,
            callback=config.callback
        )
        
        logger.info(f"Successfully created task: {config.name or 'Unnamed Task'}")
        return task
        
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return None

def create_sequential_tasks(tasks: List[Union[Dict[str, Any], TaskConfig]]) -> List[Optional[Task]]:
    """
    Create multiple tasks with sequential dependencies.
    
    Args:
        tasks: List of task configurations
        
    Returns:
        List[Optional[Task]]: List of created tasks or None for failed tasks
    """
    created_tasks = []
    for task_config in tasks:
        task = create_task(task_config)
        created_tasks.append(task)
    return created_tasks

def create_parallel_tasks(tasks: List[Union[Dict[str, Any], TaskConfig]]) -> List[Optional[Task]]:
    """
    Create multiple tasks for parallel execution.
    
    Args:
        tasks: List of task configurations
        
    Returns:
        List[Optional[Task]]: List of created tasks or None for failed tasks
    """
    created_tasks = []
    for task_config in tasks:
        if isinstance(task_config, dict):
            task_config['async_execution'] = True
        else:
            task_config.async_execution = True
        task = create_task(task_config)
        created_tasks.append(task)
    return created_tasks

def create_task_from_yaml(yaml_path: str) -> Optional[Task]:
    """
    Create a CrewAI task from a YAML configuration file.
    
    Args:
        yaml_path: Path to the YAML configuration file
        
    Returns:
        Optional[Task]: Created CrewAI task or None if creation fails
    """
    try:
        with open(yaml_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        return create_task(config_dict)
    except Exception as e:
        logger.error(f"Error creating task from YAML: {str(e)}")
        return None

def create_tasks_from_yaml(yaml_path: str) -> List[Optional[Task]]:
    """
    Create multiple tasks from a YAML configuration file.
    
    Args:
        yaml_path: Path to the YAML configuration file
        
    Returns:
        List[Optional[Task]]: List of created tasks or None for failed tasks
    """
    try:
        with open(yaml_path, 'r') as f:
            config_dicts = yaml.safe_load(f)
        if isinstance(config_dicts, list):
            return create_sequential_tasks(config_dicts)
        else:
            return [create_task(config_dicts)]
    except Exception as e:
        logger.error(f"Error creating tasks from YAML: {str(e)}")
        return [] 