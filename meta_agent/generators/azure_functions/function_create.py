"""
Azure Function creator for CrewAI specifications.
"""

import logging
import os
from pathlib import Path
from typing import Optional
import sys

from .crew_to_azure_function_generator import generate_azure_function
from .template_manager import AzureFunctionTemplateManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_azure_function(
    spec_file: str, 
    output_dir: Optional[str] = None,
    deploy: bool = False,
    function_app_name: Optional[str] = None,
    resource_group: Optional[str] = None
) -> bool:
    """
    Create an Azure Function from a CrewAI specification file.
    
    Args:
        spec_file: Path to the JSON specification file
        output_dir: Optional directory where the function will be created
                   (defaults to a directory named after the function)
        deploy: Whether to deploy the function to Azure
        function_app_name: Name of the Azure Function App (required if deploy=True)
        resource_group: Azure Resource Group name (required if deploy=True)
    
    Returns:
        bool: True if function was created successfully, False otherwise
    """
    try:
        # Validate Python version
        if sys.version_info < (3, 12):
            raise ValueError("Python 3.12 or higher is required")
            
        # Ensure spec file exists
        if not os.path.exists(spec_file):
            raise FileNotFoundError(f"Specification file not found: {spec_file}")
            
        # Create output directory if not provided
        if not output_dir:
            output_dir = Path(spec_file).stem
            
        # Initialize template manager
        template_manager = AzureFunctionTemplateManager()
        
        # Create function from template
        if not template_manager.create_function_from_template(output_dir):
            return False
            
        # Generate function code from spec
        generate_azure_function(spec_file, output_dir)
        
        # Deploy to Azure if requested
        if deploy:
            if not function_app_name or not resource_group:
                raise ValueError("function_app_name and resource_group are required for deployment")
                
            if not template_manager.deploy_to_azure(output_dir, function_app_name, resource_group):
                return False
                
        return True
            
    except Exception as e:
        logger.error(f"Error creating Azure Function: {str(e)}")
        return False
    
# deploy to azure
def deploy_to_azure(output_dir: str, function_app_name: str, resource_group: str):
    """
    Deploy the function to Azure.
    
    Args:
        output_dir: Directory containing the function
        function_app_name: Name of the Azure Function App
        resource_group: Azure Resource Group name
    """
    try:
        template_manager = AzureFunctionTemplateManager()
        template_manager.deploy_to_azure(output_dir, function_app_name, resource_group)
        return True
    except Exception as e:
        logger.error(f"Error deploying to Azure: {str(e)}")
        return False

# Example usage
if __name__ == "__main__":
    # Create locally only
    # create_azure_function(
    #     spec_file="path/to/spec.json",
    #     output_dir="my_function"
    # )

    # # Or create and deploy to Azure
    # create_azure_function(
    #     spec_file="path/to/spec.json",
    #     output_dir="my_function",
    #     deploy=True,
    #     function_app_name="my-function-app",
    #     resource_group="my-resource-group"
    # )

    # deploy to azure
    deploy_to_azure(
        output_dir="/Users/<USER>/Documents/Trail/meta-agent/my_crew_function",
        function_app_name="TestingCrew",
        resource_group="bincha-test-1_group"
    )
