{"name": "ResearchAnalysisCrew", "description": "A crew for conducting research and analysis on specified topics", "azure_config": {"llm_model": "azure/development-proactive-ai", "api_key": "********************************", "base_url": "https://bincha-ai.openai.azure.com", "function_name": "ResearchAnalysisCrew", "auth_level": "function"}, "agents": [{"name": "Marketing Analyst", "role": "Marketing Analyst", "goal": "Analyze marketing data and provide insights on {topic}. Keep it short and concise.", "backstory": "An expert Marketing Analyst with extensive experience"}, {"name": "Content Writer", "role": "Content Writer", "goal": "Write a blog post on {topic}. Keep it engaging and informative. It must be SEO friendly and around {word_count} words.", "backstory": "An expert Content Writer with extensive experience"}], "tasks": [{"description": "Analyze the marketing data and provide insights on {topic}. Keep it short and concise.", "expected_output": "A detailed marketing plan", "agent": "Marketing Analyst"}, {"description": "Write a blog post on {topic}. Keep it engaging and informative. It must be SEO friendly and around {word_count} words.", "expected_output": "A blog post", "agent": "Content Writer"}], "crew": {"process": "sequential", "verbose": true, "memory": false, "cache": true}, "configuration_keys": ["topic", "word_count"]}