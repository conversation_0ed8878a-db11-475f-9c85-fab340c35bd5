from azure.identity import DefaultAzureCredential
from azure.mgmt.resource import ResourceManagementClient
from azure.mgmt.storage import StorageManagementClient
from azure.mgmt.web import WebSiteManagementClient
from azure.mgmt.web.models import (
    Site,
    SiteConfig,
    SkuDescription,
    NameValuePair,
    HostingEnvironmentProfile
)
import os
from logger import get_logger

logger = get_logger(__name__)

def create_function_app(
    subscription_id,
    resource_group_name,
    location,
    storage_account_name,
    function_app_name,
    app_service_plan_name
):
    # Authenticate using DefaultAzureCredential
    credential = DefaultAzureCredential()

    # Initialize management clients
    resource_client = ResourceManagementClient(credential, subscription_id)
    storage_client = StorageManagementClient(credential, subscription_id)
    web_client = WebSiteManagementClient(credential, subscription_id)

    # 1. Create Resource Group
    logger.info(f"Creating Resource Group: {resource_group_name}")
    resource_client.resource_groups.create_or_update(
        resource_group_name,
        {"location": location}
    )

    # 2. Create Storage Account
    logger.info(f"Creating Storage Account: {storage_account_name}")
    storage_async_operation = storage_client.storage_accounts.begin_create(
        resource_group_name,
        storage_account_name,
        {
            "location": location,
            "sku": {"name": "Standard_LRS"},
            "kind": "StorageV2",
            "enable_https_traffic_only": True
        }
    )
    storage_account = storage_async_operation.result()

    # Retrieve storage account keys
    logger.info("Retrieving Storage Account keys")
    storage_keys = storage_client.storage_accounts.list_keys(resource_group_name, storage_account_name)
    storage_account_key = storage_keys.keys[0].value
    storage_connection_string = (
        f"DefaultEndpointsProtocol=https;AccountName={storage_account_name};"
        f"AccountKey={storage_account_key};EndpointSuffix=core.windows.net"
    )

    # 3. Create App Service Plan
    logger.info(f"Creating App Service Plan: {app_service_plan_name}")
    app_service_plan = web_client.app_service_plans.begin_create_or_update(
        resource_group_name,
        app_service_plan_name,
        {
            "location": location,
            "sku": {
                "name": "Y1",
                "tier": "Dynamic",
                "size": "Y1",
                "family": "Y",
                "capacity": 0
            },
            "reserved": True  # For Linux
        }
    ).result()

    # 4. Create Function App
    logger.info(f"Creating Function App: {function_app_name}")
    site_config = SiteConfig(
        app_settings=[
            NameValuePair(name="AzureWebJobsStorage", value=storage_connection_string),
            NameValuePair(name="FUNCTIONS_EXTENSION_VERSION", value="~4"),
            NameValuePair(name="FUNCTIONS_WORKER_RUNTIME", value="python"),
            NameValuePair(name="WEBSITE_RUN_FROM_PACKAGE", value="1")
        ],
        linux_fx_version="Python|3.12"
    )

    function_app = web_client.web_apps.begin_create_or_update(
        resource_group_name,
        function_app_name,
        Site(
            location=location,
            server_farm_id=app_service_plan.id,
            site_config=site_config,
            kind="functionapp,linux",
            reserved=True
        )
    ).result()

    logger.info(f"Function App '{function_app_name}' created successfully at URL: https://{function_app.default_host_name}")

    return function_app

def main():
    # Example usage:
    # Replace the following variables with your desired values
    subscription_id = "0c4ce32a-8bbc-4425-8ecc-49ca1efe01f5"
    resource_group_name = "bincha-test-1_group"
    location = "West US "
    storage_account_name = "binchatest1group"
    function_app_name = "TestingCrew"
    app_service_plan_name = "binchatest1group"

    try:
        create_function_app(
            subscription_id,
            resource_group_name,
            location,
            storage_account_name,
            function_app_name,
            app_service_plan_name
        )
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    main()
