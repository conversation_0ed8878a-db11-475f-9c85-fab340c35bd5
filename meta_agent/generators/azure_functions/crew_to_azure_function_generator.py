"""
Generator for creating Azure Functions from CrewAI specifications.
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import shutil
from dotenv import load_dotenv
from meta_agent.utils.file_utils import get_project_root

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# reading the .env file
load_dotenv()

# get azure config from .env file
azure_config = {
    'llm_model': os.getenv('AZURE_LLM_MODEL'),
    'api_key': os.getenv('AZURE_API_KEY'),
    'base_url': os.getenv('AZURE_BASE_URL')
}

class AzureFunctionGenerator:
    """Generator for creating Azure Functions from CrewAI specifications."""
    
    def __init__(self, spec_file: str, output_dir: str):
        """
        Initialize the generator.
        
        Args:
            spec_file: Path to the JSON specification file
            output_dir: Directory where Azure Function files will be generated
        """
        self.spec_file = spec_file
        self.output_dir = output_dir
        self.spec = self._load_spec()
        
    def _load_spec(self) -> Dict[str, Any]:
        """Load and validate the JSON specification."""
        try:
            with open(self.spec_file, 'r') as f:
                spec = json.load(f)
                
            required_fields = ['name', 'description', 'agents', 'tasks', 'crew', 'configuration_keys']
            missing = [field for field in required_fields if field not in spec]
            if missing:
                raise ValueError(f"Missing required fields in spec: {', '.join(missing)}")
                
            return spec
        except Exception as exc:
            logger.error(f"Error loading spec file: {str(exc)}")
            raise
            
    def _generate_tools(self) -> str:
        """Generate tool definitions from the spec."""
        tools_code = "# Define tools using the @tool decorator\n"
        
        if 'tools' in self.spec:
            for tool in self.spec['tools']:
                tools_code += f"""
@tool("{tool['name']}")
def {tool['function_name']}({', '.join(tool['parameters'])}):
    \"""{tool['description']}\"""
    {tool['implementation']}
"""
        return tools_code
        
    def _generate_crew_class(self) -> str:
        """Generate the CrewAI class implementation."""
        try:
            class_name = self.spec['name'].replace(' ', '')
            config_keys = self.spec.get('configuration_keys', [])
            
            crew_code = f"""
class {class_name}:
    def __init__(self, request_data: Dict[str, Any]):
        self.request_data = request_data
        
        # Initialize the LLM with environment variables
        self.llm = LLM(
            model=os.getenv('AZURE_LLM_MODEL'),
            api_key=os.getenv('AZURE_API_KEY'),
            base_url=os.getenv('AZURE_BASE_URL')
        )
        
        # Initialize components
        self.agents = self._create_agents()
        self.tasks = self._create_tasks()
        self.crew = self._create_crew()
        
    def _create_agents(self):
        \"\"\"Create and configure agents.\"\"\"
        agents = {{}}
"""
            
            # Add agents with proper string formatting
            for agent in self.spec['agents']:
                tools = agent.get('tools', [])
                tools_str = f"[{', '.join(tools)}]" if tools else "[]"
                backstory = agent.get('backstory', f'An expert {agent["role"]} with extensive experience')
                
                crew_code += f"""
        agents["{agent['name']}"] = Agent(
            role="{agent['role']}",
            goal="{agent['goal']}",
            backstory="{backstory}",
            tools={tools_str},
            llm=self.llm
        )
"""
            
            crew_code += """
        return agents
        
    def _create_tasks(self):
        \"\"\"Create and configure tasks.\"\"\"
        tasks = []
"""
            
            # Add tasks with proper string formatting
            for task in self.spec['tasks']:
                expected_output = task.get('expected_output', 'Complete the task effectively')
                crew_code += f"""
        tasks.append(Task(
            description="{task['description']}",
            expected_output="{expected_output}",
            agent=self.agents["{task['agent']}"]
        ))
"""
            
            # Add crew creation with proper configuration
            crew_code += f"""
        return tasks
        
    def _create_crew(self):
        \"\"\"Create and configure the crew.\"\"\"
        crew_config = self.spec.get('crew', {{}})
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=getattr(Process, crew_config.get('process', 'sequential')),
            verbose=crew_config.get('verbose', True),
            memory=crew_config.get('memory', False),
            cache=crew_config.get('cache', True)
        )
        
    def run(self):
        \"\"\"Execute the crew's tasks using the request data.\"\"\"
        try:
            return self.crew.kickoff(inputs=self.request_data)
        except Exception as exc:
            logger.error(f"Error running crew: {{str(exc)}}")
            logger.exception("Full stack trace:")
            raise
"""
            return crew_code
            
        except Exception as exc:
            logger.error(f"Error generating crew class: {{str(exc)}}")
            logger.exception("Full stack trace:")
            raise

    def _generate_function_app(self) -> str:
        """Generate the main function_app.py content."""
        class_name = self.spec['name'].replace(' ', '')
        route_name = class_name
        config_keys = self.spec.get('configuration_keys', [])
        config_args = ', '.join(config_keys)
        config_extract = '\n'.join([
            f"      {k} = request_data.get(\"{k}\")" for k in config_keys
        ])
        config_pass = ', '.join(config_keys)

        return f'''import logging
import azure.functions as func
from crewai import Agent, Task, Crew, Process, LLM
from crewai.tools import tool
from typing import Dict, Any

# Define the FunctionApp instance
app = func.FunctionApp(http_auth_level=func.AuthLevel.FUNCTION)

{self._generate_tools()}

{self._generate_crew_class()}

@app.route(route="{route_name}", methods=["POST"])
def {route_name.lower()}_handler(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("{class_name} function triggered.")

    try:
        # Get JSON body
        request_data = req.get_json()
    except ValueError:
        return func.HttpResponse(
            "Please pass a valid JSON object in the request body",
            status_code=400
        )

    try:
        crew = {class_name}(request_data=request_data)
        logging.info(f"Request data: {{request_data}}")
        result = crew.run(request_data)
        return func.HttpResponse(
            f"Output:{{result}}",
            status_code=200
        )
    except Exception as e:
        logging.error(f"Error running crew: {{str(e)}}")
        return func.HttpResponse(
            f"Error processing request: {{str(e)}}",
            status_code=500
        )

# GET handler for health check
@app.route(route="{route_name}", methods=["GET"])
def {route_name.lower()}_health_check(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Health check - GET call.")
    return func.HttpResponse("Hello, World!", status_code=200)
'''

    def _generate_requirements(self) -> str:
        """Generate requirements.txt content."""
        return '''# Core dependencies
azure-functions>=1.17.0
crewai>=0.11.0
langchain>=0.1.0
langchain-openai>=0.0.2
openai>=1.3.0
python-dotenv>=1.0.0

# Additional dependencies
requests>=2.31.0
beautifulsoup4>=4.12.2
pandas>=2.1.3
numpy>=1.24.3'''

    def generate(self):
        """Generate all Azure Function files."""
        try:
            # Create output directory
            os.makedirs(self.output_dir, exist_ok=True)
            
            # Generate function_app.py
            with open(os.path.join(self.output_dir, 'function_app.py'), 'w') as f:
                f.write(self._generate_function_app())
                
            # Generate requirements.txt
            with open(os.path.join(self.output_dir, 'requirements.txt'), 'w') as f:
                f.write(self._generate_requirements())
                
            # Copy host.json and other template files
            template_dir = Path(__file__).parent.parent.parent.parent / 'template' / 'azure_funcitons'
            for file in ['host.json', '.ostype', 'oryx-manifest.toml']:
                if (template_dir / file).exists():
                    shutil.copy2(template_dir / file, self.output_dir)
                    
            logger.info(f"Successfully generated Azure Function in {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Error generating Azure Function: {str(e)}")
            raise

def generate_azure_function(spec_file: str, output_dir: str):
    """
    Generate an Azure Function from a CrewAI specification file.
    
    Args:
        spec_file: Path to the JSON specification file
        output_dir: Directory where Azure Function files will be generated
    """
    generator = AzureFunctionGenerator(spec_file, output_dir)
    generator.generate()

def _get_project_root() -> Path:
    """Get the project root path from .env file or fallback to current behavior."""
    # Use the new utility for consistency
    return Path(get_project_root())

def _copy_multifunction_template(output_dir: str):
    """Copy shared template files for multi-function Azure Functions."""
    from pathlib import Path
    import shutil
    template_dir = Path(get_project_root()) / 'template' / 'multifunction-azure'
    for file in ['host.json', '.ostype', 'oryx-manifest.toml', 'local.settings.json', 'requirements.txt']:
        src = template_dir / file
        if src.exists():
            shutil.copy2(src, output_dir)

def _write_function_json(target_dir: str, route_name: str, methods: list):
    import json
    function_json = {
        "scriptFile": "__init__.py",
        "bindings": [
            {
                "authLevel": "function",
                "type": "httpTrigger",
                "direction": "in",
                "name": "req",
                "methods": methods,
                "route": route_name
            },
            {
                "type": "http",
                "direction": "out",
                "name": "$return"
            }
        ]
    }
    with open(os.path.join(target_dir, 'function.json'), 'w') as f:
        json.dump(function_json, f, indent=2)

class AzureMultiFunctionGenerator:
    """Generator for creating multi-function Azure Functions from CrewAI specifications."""
    
    def __init__(self, spec_file: str, output_dir: str):
        """
        Initialize the generator.
        
        Args:
            spec_file: Path to the JSON specification file
            output_dir: Directory where Azure Function files will be generated
        """
        self.spec_file = spec_file
        self.output_dir = output_dir
        self.spec = self._load_spec()
        self.azure_config = {
            'llm_model': os.getenv('AZURE_LLM_MODEL'),
            'api_key': os.getenv('AZURE_API_KEY'),
            'base_url': os.getenv('AZURE_BASE_URL')
        }
        
        # Validate required environment variables
        missing_vars = [k for k, v in self.azure_config.items() if not v]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    def _load_spec(self) -> Dict[str, Any]:
        """Load and validate the JSON specification."""
        try:
            with open(self.spec_file, 'r') as f:
                spec = json.load(f)
                
            required_fields = ['name', 'description', 'agents', 'tasks', 'crew', 'configuration_keys']
            missing = [field for field in required_fields if field not in spec]
            if missing:
                raise ValueError(f"Missing required fields in spec: {', '.join(missing)}")
                
            return spec
        except Exception as exc:
            logger.error(f"Error loading spec file: {str(exc)}")
            logger.exception("Full stack trace:")
            raise

    def _generate_tools(self) -> str:
        """Generate tool definitions from the spec."""
        tools_code = "# Define tools using the @tool decorator\n"
        
        if 'tools' in self.spec:
            for tool in self.spec['tools']:
                tools_code += f"""
@tool("{tool['name']}")
def {tool['function_name']}({', '.join(tool['parameters'])}):
    \"\"\"{tool['description']}\"\"\"
    {tool['implementation']}
"""
        return tools_code

    def _generate_crew_class(self) -> str:
        """Generate the CrewAI class implementation."""
        try:
            class_name = self.spec['name'].replace(' ', '')
            config_keys = self.spec.get('configuration_keys', [])

            # Build up the code as a single triple-quoted string
            crew_code = f"""
class {class_name}:
    def __init__(self, request_data: Dict[str, Any]):
        self.request_data = request_data
        # Initialize the LLM with environment variables
        self.llm = LLM(
            model=\"azure/development-proactive-ai\",
            api_key=\"********************************\",
            base_url=\"https://bincha-ai.openai.azure.com\"
        )
        # Initialize components
        self.agents = self._create_agents()
        self.tasks = self._create_tasks()
        self.crew = self._create_crew()

    def _create_agents(self):
        \"\"\"Create and configure agents.\"\"\"
        agents = {{}}
        # Static first agent: Input & Output Specifier
        agents[\"Input & Output Specifier\"] = Agent(
            role=\"Input & Output Specifier\",
            goal=\"Pass the user input and required output format (as a string template) to the crew.\",
            backstory=\"You provide the data and output format for the crew to work on.\",
            llm=self.llm
        )
"""
            # Dynamic agents from the spec
            for agent in self.spec['agents']:
                tools = agent.get('tools', [])
                tools_str = f"[{', '.join(tools)}]" if tools else "[]"
                backstory = agent.get('backstory', f'An expert {agent["role"]} with extensive experience')
                crew_code += f"""
        agents[\"{agent['name']}\"] = Agent(
            role=\"{agent['role']}\",
            goal=\"{agent['goal']}\",
            backstory=\"{backstory}\",
            llm=self.llm
        )
"""
            # Static last agent: Output Formatter & Validator
            crew_code += f"""
        agents[\"Output Formatter & Validator\"] = Agent(
            role=\"Output Formatter & Validator\",
            goal=\"Format the crew's output as specified and validate that all required keys (inferred from the output format string provided by the first agent) are present. If not, regenerate up to 3 times.\",
            backstory=\"You ensure the output is correctly formatted and complete before sending it out.\",
            llm=self.llm
        )
        return agents

    def _create_tasks(self):
        \"\"\"Create and configure tasks.\"\"\"
        tasks = []
        # Static first task: Input & Output Specifier
        tasks.append(Task(
            description=\"Pass the user input and required output format (as a string template) to the crew.\",
            expected_output=\"The user input and output format string are available to the crew.\",
            agent=self.agents[\"Input & Output Specifier\"]
        ))
"""
            # Dynamic tasks from the spec
            for task in self.spec['tasks']:
                expected_output = task.get('expected_output', 'Complete the task effectively')
                crew_code += f"""
        tasks.append(Task(
            description=\"{task['description']}\",
            expected_output=\"{expected_output}\",
            agent=self.agents[\"{task['agent']}\"]
        ))
"""
            # Static last task: Output Formatter & Validator
            crew_code += """
        tasks.append(Task(
            description=\"Format the output of the crew into the required output format (provided as a string template). Infer the required keys from the template. Validate that all required keys are present in the output. If any are missing, regenerate the output, up to 3 times.\",
            expected_output=\"A valid output string matching the required format, with all required keys present.\",
            agent=self.agents[\"Output Formatter & Validator\"]
        ))
        return tasks

    def _create_crew(self):
        \"\"\"Create and configure the crew.\"\"\"
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,
            cache=True
        )

    def run(self):
        \"\"\"Execute the crew's tasks using the request data.\"\"\"
        try:
            return self.crew.kickoff(inputs=self.request_data)
        except Exception as exc:
            print(f'Error running crew: {{str(exc)}}')
            raise
"""
            return crew_code
        except Exception as exc:
            logger.error(f"Error generating crew class: {str(exc)}")
            logger.exception("Full stack trace:")
            raise

    def _generate_function_init(self, class_name: str, route_name: str) -> str:
        """Generate the function initialization code."""
        try:
            return f'''import logging
import os
from typing import Dict, Any
import azure.functions as func
from crewai import Agent, Task, Crew, Process, LLM
from crewai.tools import tool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

{self._generate_tools()}

{self._generate_crew_class()}

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("{class_name} function triggered.")
    
    try:
        # Get JSON body
        request_data = req.get_json()
    except ValueError as json_err:
        logging.error(f"Invalid JSON in request: {{str(json_err)}}")
        return func.HttpResponse(
            "Please pass a valid JSON object in the request body",
            status_code=400
        )
        
    try:
        # Initialize and run the crew
        crew = {class_name}(request_data=request_data)
        logging.info(f"Processing request with data: {{request_data}}")
        result = crew.run()
        
        return func.HttpResponse(
            f"Output:{{result}}",
            status_code=200
        )
    except Exception as exc:
        logging.error(f"Error processing request: {{str(exc)}}")
        logging.exception("Full stack trace:")
        return func.HttpResponse(
            f"Error processing request: {{str(exc)}}",
            status_code=500
        )
'''
        except Exception as exc:
            logger.error(f"Error generating function initialization code: {{str(exc)}}")
            logger.exception("Full stack trace:")
            raise

    def _generate_health_check_init(self) -> str:
        return '''import logging
import azure.functions as func

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Health check - GET call.")
    return func.HttpResponse("Hello, I'm alive!", status_code=200)
'''

    def generate(self):
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            _copy_multifunction_template(self.output_dir)
            # Always create health_check function
            health_dir = os.path.join(self.output_dir, 'health_check')
            os.makedirs(health_dir, exist_ok=True)
            with open(os.path.join(health_dir, '__init__.py'), 'w') as f:
                f.write(self._generate_health_check_init())
            _write_function_json(health_dir, 'health_check', ['get'])
            # Create the main function
            func_name = self.spec['name'].replace(' ', '').lower()
            func_dir = os.path.join(self.output_dir, func_name)
            os.makedirs(func_dir, exist_ok=True)
            class_name = self.spec['name'].replace(' ', '')
            with open(os.path.join(func_dir, '__init__.py'), 'w') as f:
                f.write(self._generate_function_init(class_name, func_name))
            _write_function_json(func_dir, func_name, ['post'])
            logger.info(f"Successfully generated multi-function Azure Function in {self.output_dir}")
        except Exception as e:
            logger.error(f"Error generating multi-function Azure Function: {str(e)}")
            raise

def generate_azure_multi_function(spec_file: str, output_dir: str):
    """
    Generate a multi-function Azure Function from a CrewAI specification file.
    Args:
        spec_file: Path to the JSON specification file
        output_dir: Directory where Azure Function files will be generated
    """
    generator = AzureMultiFunctionGenerator(spec_file, output_dir)
    generator.generate()

def add_function_script(directory_name: str, spec_file: str):
    """
    Adds a new function script to an existing crew_scripts directory.
    
    Args:
        directory_name: Name of the directory under <root_of_the_project>/crew_scripts/
        spec_file: Path to the crew spec JSON file
        
    Raises:
        FileNotFoundError: If the target directory or spec file doesn't exist
        FileExistsError: If the function folder already exists and shouldn't be overwritten
        ValueError: If there are issues with the spec file or configuration
        Exception: For any other unexpected errors
    """
    try:
        # 1. Locate the directory
        root_dir = Path(__file__).parent.parent.parent.parent
        target_dir = root_dir / 'crew_scripts' / directory_name
        
        if not target_dir.exists():
            raise FileNotFoundError(f"Target directory {target_dir} does not exist.")
            
        if not os.path.exists(spec_file):
            raise FileNotFoundError(f"Spec file {spec_file} does not exist.")
            
        # 2. Load and validate the spec
        try:
            with open(spec_file, 'r') as f:
                spec = json.load(f)
        except json.JSONDecodeError as json_err:
            raise ValueError(f"Invalid JSON in spec file: {str(json_err)}")
        except Exception as spec_err:
            raise ValueError(f"Error reading spec file: {str(spec_err)}")
            
        # Validate required fields
        required_fields = ['name', 'description', 'agents', 'tasks', 'crew', 'configuration_keys']
        missing = [field for field in required_fields if field not in spec]
        if missing:
            raise ValueError(f"Missing required fields in spec: {', '.join(missing)}")
            
        class_name = spec['name'].replace(' ', '')
        func_folder = target_dir / class_name
        
        # 3. Check if function folder exists and handle accordingly
        if func_folder.exists():
            logger.info(f"Function folder {func_folder} already exists. Overwriting with new spec.")
            # Remove existing files to ensure clean state
            for file in func_folder.glob('*'):
                if file.is_file():
                    file.unlink()
        else:
            logger.info(f"Creating new function folder: {func_folder}")
            os.makedirs(func_folder, exist_ok=False)
            
        # 4. Generate function files using multi-function logic
        try:
            generator = AzureMultiFunctionGenerator(spec_file, str(func_folder))
            # Only generate the function logic, not shared files or health_check
            init_code = generator._generate_function_init(class_name, class_name.lower())
            
            # Write the function files
            with open(func_folder / '__init__.py', 'w') as f:
                f.write(init_code)
                
            _write_function_json(str(func_folder), class_name.lower(), ['post'])
            
            logger.info(f"Successfully added/updated function script at {func_folder}")
            return True
            
        except Exception as gen_err:
            logger.error(f"Error generating function files: {str(gen_err)}")
            # Clean up on failure
            if func_folder.exists():
                shutil.rmtree(func_folder)
            raise
            
    except (FileNotFoundError, FileExistsError, ValueError) as known_err:
        logger.error(f"Error adding function script: {str(known_err)}")
        raise
    except Exception as unknown_err:
        logger.error(f"Unexpected error adding function script: {str(unknown_err)}")
        logger.exception("Full stack trace:")
        raise

class AzureMultiFunctionGeneratorForTemplates:
    """Generator for creating multi-function Azure Functions from CrewAI specifications for templates."""
    
    def __init__(self, spec_file: str, output_dir: str):
        """
        Initialize the generator.
        
        Args:
            spec_file: Path to the JSON specification file
            output_dir: Directory where Azure Function files will be generated
        """
        self.spec_file = spec_file
        self.output_dir = output_dir
        self.spec = self._load_spec()
        self.azure_config = {
            'llm_model': os.getenv('AZURE_LLM_MODEL'),
            'api_key': os.getenv('AZURE_API_KEY'),
            'base_url': os.getenv('AZURE_BASE_URL')
        }
        
        # Validate required environment variables
        missing_vars = [k for k, v in self.azure_config.items() if not v]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    def _load_spec(self) -> Dict[str, Any]:
        """Load and validate the JSON specification."""
        try:
            with open(self.spec_file, 'r') as f:
                spec = json.load(f)
                
            required_fields = ['name', 'description', 'agents', 'tasks', 'crew', 'configuration_keys']
            missing = [field for field in required_fields if field not in spec]
            if missing:
                raise ValueError(f"Missing required fields in spec: {', '.join(missing)}")
                
            return spec
        except Exception as exc:
            logger.error(f"Error loading spec file: {str(exc)}")
            logger.exception("Full stack trace:")
            raise

    def _generate_tools(self) -> str:
        """Generate tool definitions from the spec."""
        tools_code = "# Define tools using the @tool decorator\n"
        
        if 'tools' in self.spec:
            for tool in self.spec['tools']:
                tools_code += f"""
@tool("{tool['name']}")
def {tool['function_name']}({', '.join(tool['parameters'])}):
    \"\"\"{tool['description']}\"\"\"
    {tool['implementation']}
"""
        return tools_code

    def _generate_crew_class(self) -> str:
        """Generate the CrewAI class implementation."""
        try:
            class_name = self.spec['name'].replace(' ', '')
            config_keys = self.spec.get('configuration_keys', [])

            # Build up the code as a single triple-quoted string
            crew_code = f"""
class {class_name}:
    def __init__(self, request_data: Dict[str, Any]):
        self.request_data = request_data
        # Initialize the LLM with environment variables
        self.llm = LLM(
            model=\"azure/development-proactive-ai\",
            api_key=\"********************************\",
            base_url=\"https://bincha-ai.openai.azure.com\"
        )
        # Initialize components
        self.agents = self._create_agents()
        self.tasks = self._create_tasks()
        self.crew = self._create_crew()

    def _create_agents(self):
        \"\"\"Create and configure agents.\"\"\"
        agents = {{}}
        # Static first agent: Input & Output Specifier
        agents[\"Input & Output Specifier\"] = Agent(
            role=\"Input & Output Specifier\",
            goal=\"Pass the user input and required output format (as a string template) to the crew.\",
            backstory=\"You provide the data and output format for the crew to work on.\",
            verbose=True,
            llm=self.llm
        )
"""
            # Dynamic agents from the spec
            for agent in self.spec['agents']:
                tools = agent.get('tools', [])
                tools_str = f"[{', '.join(tools)}]" if tools else "[]"
                backstory = agent.get('backstory', f'An expert {agent["role"]} with extensive experience')
                crew_code += f"""
        agents[\"{agent['name']}\"] = Agent(
            role=\"{agent['role']}\",
            goal=\"{agent['goal']}\",
            backstory=\"{backstory}\",
            verbose=True,
            llm=self.llm
        )
"""
            # Static last agent: Output Formatter & Validator
            crew_code += f"""
        agents[\"Output Formatter & Validator\"] = Agent(
            role=\"Output Formatter & Validator\",
            goal=\"Format the crew's output as specified and validate that all required keys (inferred from the output format string provided by the first agent) are present. If not, regenerate up to 3 times.\",
            backstory=\"You ensure the output is correctly formatted and complete before sending it out.\",
            verbose=True,
            llm=self.llm
        )
        return agents

    def _create_tasks(self):
        \"\"\"Create and configure tasks.\"\"\"
        tasks = []
        # Static first task: Input & Output Specifier
        tasks.append(Task(
            description=\"Pass the user input and required output format (as a string template) to the crew. input - {{input_data}} output - {{output_structure}}\",
            expected_output=\"The user input and output format string are available to the crew.\",
            agent=self.agents[\"Input & Output Specifier\"]
        ))
"""
            # Dynamic tasks from the spec
            for task in self.spec['tasks']:
                expected_output = task.get('expected_output', 'Complete the task effectively')
                crew_code += f"""
        tasks.append(Task(
            description=\"{task['description']}\",
            expected_output=\"{expected_output}\",
            agent=self.agents[\"{task['agent']}\"]
        ))
"""
            # Static last task: Output Formatter & Validator
            crew_code += """
        tasks.append(Task(
            description=\"Format the output of the crew into the required output format (provided as a string template). Infer the required keys from the template. Validate that all required keys are present in the output. If any are missing, regenerate the output, up to 3 times. output_format - {output_structure}\",
            expected_output=\"A valid output string matching the required format, with all required keys present in JSON format.\",
            agent=self.agents[\"Output Formatter & Validator\"]
        ))
        return tasks

    def _create_crew(self):
        \"\"\"Create and configure the crew.\"\"\"
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,
            cache=True
        )

    def run(self):
        \"\"\"Execute the crew's tasks using the request data.\"\"\"
        try:
            return self.crew.kickoff(inputs=self.request_data)
        except Exception as exc:
            print(f'Error running crew: {{str(exc)}}')
            raise
"""
            return crew_code
        except Exception as exc:
            logger.error(f"Error generating crew class: {str(exc)}")
            logger.exception("Full stack trace:")
            raise

    def _generate_function_init(self, class_name: str, route_name: str) -> str:
        """Generate the function initialization code."""
        try:
            return f'''import logging
import os
from typing import Dict, Any
import azure.functions as func
from crewai import Agent, Task, Crew, Process, LLM
from crewai.tools import tool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

{self._generate_tools()}

{self._generate_crew_class()}

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("{class_name} function triggered.")
    
    try:
        # Get JSON body
        request_data = req.get_json()
    except ValueError as json_err:
        logging.error(f"Invalid JSON in request: {{str(json_err)}}")
        return func.HttpResponse(
            "Please pass a valid JSON object in the request body",
            status_code=400
        )
        
    try:
        # Initialize and run the crew
        crew = {class_name}(request_data=request_data)
        logging.info(f"Processing request with data: {{request_data}}")
        result = crew.run()
        
        return func.HttpResponse(
            f"Output:{{result}}",
            status_code=200
        )
    except Exception as exc:
        logging.error(f"Error processing request: {{str(exc)}}")
        logging.exception("Full stack trace:")
        return func.HttpResponse(
            f"Error processing request: {{str(exc)}}",
            status_code=500
        )
'''
        except Exception as exc:
            logger.error(f"Error generating function initialization code: {{str(exc)}}")
            logger.exception("Full stack trace:")
            raise

    def _generate_health_check_init(self) -> str:
        return '''import logging
import azure.functions as func

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Health check - GET call.")
    return func.HttpResponse("Hello, I'm alive!", status_code=200)
'''

    def generate(self):
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            _copy_multifunction_template(self.output_dir)
            # Always create health_check function
            health_dir = os.path.join(self.output_dir, 'health_check')
            os.makedirs(health_dir, exist_ok=True)
            with open(os.path.join(health_dir, '__init__.py'), 'w') as f:
                f.write(self._generate_health_check_init())
            _write_function_json(health_dir, 'health_check', ['get'])
            # Create the main function
            func_name = self.spec['name'].replace(' ', '').lower()
            func_dir = os.path.join(self.output_dir, func_name)
            os.makedirs(func_dir, exist_ok=True)
            class_name = self.spec['name'].replace(' ', '')
            with open(os.path.join(func_dir, '__init__.py'), 'w') as f:
                f.write(self._generate_function_init(class_name, func_name))
            _write_function_json(func_dir, func_name, ['post'])
            logger.info(f"Successfully generated multi-function Azure Function in {self.output_dir}")
        except Exception as e:
            logger.error(f"Error generating multi-function Azure Function: {str(e)}")
            raise

def generate_template_crew_function(spec_file: str, output_dir: str):
    """
    Generate a template-based crew function using AzureMultiFunctionGeneratorForTemplates.
    
    Args:
        spec_file: Path to the JSON specification file
        output_dir: Directory where Azure Function files will be generated
    """
    try:
        generator = AzureMultiFunctionGeneratorForTemplates(spec_file, output_dir)
        generator.generate()
        return True
    except Exception as e:
        logger.error(f"Error generating template crew function: {str(e)}")
        raise

def _copy_template_to_folder_for_template(output_dir: str) -> bool:
    """Copy template files for template-based crew functions."""
    import os
    template_dir = os.path.join(get_project_root(), "template", "multifunction-azure")
    try:
        import shutil
        shutil.copytree(template_dir, output_dir, dirs_exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to copy template: {e}")
        return False

def _write_template_function_json(target_dir: str, route_name: str, methods: list):
    """Write function.json for template-based crew functions."""
    import json
    function_json = {
        "scriptFile": "__init__.py",
        "bindings": [
            {
                "authLevel": "function",
                "type": "httpTrigger",
                "direction": "in",
                "name": "req",
                "methods": methods,
                "route": route_name
            },
            {
                "type": "http",
                "direction": "out",
                "name": "$return"
            }
        ]
    }
    with open(os.path.join(target_dir, 'function.json'), 'w') as f:
        json.dump(function_json, f, indent=2)
