"""
Template manager for Azure Functions.
"""

import logging
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AzureFunctionTemplateManager:
    """Manages the base template for Azure Functions."""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent.parent.parent.parent / "template" / "azure_funcitons"
        
    def create_base_template(self) -> bool:
        """
        Create a base template for Azure Functions if it doesn't exist.
        This only needs to be run once to set up the template.
        """
        try:
            if self.template_dir.exists():
                logger.info("Template directory already exists")
                return True
                
            # Create template directory
            self.template_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize function project
            subprocess.run(
                ["func", "init", str(self.template_dir), "--worker-runtime", "python", "--python"],
                check=True
            )
            
            # Create requirements.txt with all necessary dependencies
            requirements = '''# Core dependencies
azure-functions>=1.17.0
crewai>=0.11.0
langchain>=0.1.0
langchain-openai>=0.0.2
openai>=1.3.0
python-dotenv>=1.0.0

# Additional dependencies
requests>=2.31.0
beautifulsoup4>=4.12.2
pandas>=2.1.3
numpy>=1.24.3'''
            
            with open(self.template_dir / "requirements.txt", "w") as f:
                f.write(requirements)
                
            # Create virtual environment
            subprocess.run(
                [sys.executable, "-m", "venv", ".venv"],
                cwd=self.template_dir,
                check=True
            )
            
            # Install dependencies
            venv_python = self.template_dir / ".venv" / ("Scripts" if os.name == "nt" else "bin") / "python"
            subprocess.run(
                [str(venv_python), "-m", "pip", "install", "-r", "requirements.txt"],
                cwd=self.template_dir,
                check=True
            )
            
            logger.info(f"Successfully created base template in {self.template_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating base template: {str(e)}")
            return False
            
    def create_function_from_template(self, output_dir: str) -> bool:
        """
        Create a new function from the base template.
        
        Args:
            output_dir: Directory where the function will be created
        """
        try:
            # Ensure template exists
            if not self.template_dir.exists():
                if not self.create_base_template():
                    return False
            
            # Create output directory
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Copy template files
            for item in self.template_dir.iterdir():
                if item.name != ".venv":  # Skip virtual environment
                    if item.is_dir():
                        shutil.copytree(item, output_path / item.name, dirs_exist_ok=True)
                    else:
                        shutil.copy2(item, output_path / item.name)
            
            logger.info(f"Successfully created function from template in {output_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating function from template: {str(e)}")
            return False
            
    def deploy_to_azure(self, function_dir: str, function_app_name: str, resource_group: str) -> bool:
        """
        Deploy the function to Azure.
        
        Args:
            function_dir: Directory containing the function
            function_app_name: Name of the Azure Function App
            resource_group: Azure Resource Group name
        """
        try:
            # Deploy to Azure
            subprocess.run(
                ["func", "azure", "functionapp", "publish", function_app_name, "--python"],
                cwd=function_dir,
                check=True
            )
            
            logger.info(f"Successfully deployed {function_app_name} to Azure")
            return True
            
        except Exception as e:
            logger.error(f"Error deploying to Azure: {str(e)}")
            return False 

# template manager for azure multi-functions

class AzureMultiFunctionTemplateManager:
    """Manages the base template for Azure Multi-Functions."""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent.parent.parent.parent / "template" / "multifunction-azure"

    def create_base_template(self) -> bool:
        """
        Create a base template for Azure Multi-Functions if it doesn't exist.
        This only needs to be run once to set up the template.
        """
        
        try:
            if self.template_dir.exists():
                logger.info("Template directory already exists")
                return True
            
            # Create template directory
            self.template_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize function project
            subprocess.run(
                ["func", "init", str(self.template_dir), "--worker-runtime", "python", "--python"],
                check=True
            )

            #delete the default function_app file
            (self.template_dir / "function_app.py").unlink()

            # Create requirements.txt with all necessary dependencies
            requirements = '''# Core dependencies
azure-functions>=1.17.0
crewai>=0.11.0
langchain>=0.1.0
langchain-openai>=0.0.2
openai>=1.3.0
python-dotenv>=1.0.0

# Additional dependencies
requests>=2.31.0
beautifulsoup4>=4.12.2
pandas>=2.1.3
numpy>=1.24.3'''

            with open(self.template_dir / "requirements.txt", "w") as f:
                f.write(requirements)

            #create a directory for health_check function
            (self.template_dir / "health_check").mkdir(parents=True, exist_ok=True)

            # Create health_check/__init__.py
            health_check_init = '''import logging
import azure.functions as func

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Health check - GET call.")
    return func.HttpResponse("Hello, I'm alive!", status_code=200)
'''
            with open(self.template_dir / "health_check" / "__init__.py", "w") as f:
                f.write(health_check_init)

            # Create health_check/function.json
            health_check_function_json = '''{
  "scriptFile": "__init__.py",
  "bindings": [
    {
      "authLevel": "function",
      "type": "httpTrigger",
      "direction": "in",
      "name": "req",
      "methods": [
        "get"
      ],
      "route": "health_check"
    },
    {
      "type": "http",
      "direction": "out",
      "name": "$return"
    }
  ]
}'''
            with open(self.template_dir / "health_check" / "function.json", "w") as f:
                f.write(health_check_function_json)

            # Create virtual environment
            subprocess.run(
                [sys.executable, "-m", "venv", ".venv"],
                cwd=self.template_dir,
                check=True
            )
    
            logger.info(f"Successfully created base template in {self.template_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating base template: {str(e)}")
            return False
    