#Actual code
import logging
import azure.functions as func
from crewai import Agent, Task, Crew, Process, LLM
from crewai.tools import tool  # Adjust this import based on your LLM setup

# Define the FunctionApp instance for the v2 model
app = func.FunctionApp(http_auth_level=func.AuthLevel.FUNCTION)

# Define tools using the @tool decorator
@tool("Task Management")
def task_management(task_name: str, status: str, assignee: str) -> str:
    """Manage and track research tasks."""
    return f"Task '{task_name}' is marked as '{status}' and assigned to '{assignee}'."

@tool("Web Search")
def web_search(query: str, max_results: int = 5) -> str:
    """Perform web-based research."""
    return f"Performed web search for '{query}' with max {max_results} results."

@tool("Text Analysis")
def text_analysis(text: str, analysis_type: str) -> str:
    """Analyze text content."""
    return f"Performed '{analysis_type}' analysis on the provided text."

@tool("Document Review")
def document_review(document: str, review_criteria: str) -> str:
    """Review documents based on specified criteria."""
    return f"Reviewed document based on criteria: {review_criteria}."

@tool("Data Validation")
def data_validation(data: str, validation_rules: str) -> str:
    """Validate data accuracy."""
    return f"Validated data with rules: {validation_rules}."

@tool("Summary Generation")
def summary_generation(content: str, max_length: int = 100) -> str:
    """Generate summaries of the content."""
    return f"Generated summary of length {max_length} for the provided content."

class ResearchAnalysisCrew:
    # Accept topic in constructor
    def __init__(self, topic: str):
        self.topic = topic # Store topic

        # Initialize the LLM
        self.llm = LLM(
            model="azure/development-proactive-ai",
            api_key="********************************",
            base_url="https://bincha-ai.openai.azure.com/openai/deployments/development-proactive-ai/chat/completions?api-version=2024-08-01-preview"
        )

        # Initialize agents - they will use self.topic now
        self.agents = self._create_agents()

        # Initialize tasks - they will use self.topic now
        self.tasks = self._create_tasks()

        # Initialize crew
        self.crew = self._create_crew()

    def _create_agents(self):
        """Create and configure agents, using self.topic."""
        return {
            "Research Coordinator": Agent(
                role="Research Coordinator",
                # Use self.topic in goal
                goal=f"Coordinate research activities, define scope, and compile reports on {self.topic}.",
                backstory="An expert Research Coordinator with extensive experience.",
                tools=[task_management, document_review],
                llm=self.llm
            ),
            "Data Researcher": Agent(
                role="Data Researcher",
                # Use self.topic in goal (implicitly via tasks)
                goal=f"Collect data, verify accuracy, and organize research findings on {self.topic}.",
                backstory="An expert Data Researcher with access to vast information sources.",
                tools=[web_search, data_validation],
                llm=self.llm
            ),
            "Content Analyst": Agent(
                role="Content Analyst",
                # Use self.topic in goal (implicitly via tasks)
                goal=f"Analyze data, identify insights, and generate summaries related to {self.topic}.",
                backstory="An expert in distilling complex content into actionable insights.",
                tools=[text_analysis, summary_generation],
                llm=self.llm
            )
        }

    def _create_tasks(self):
        """Create and configure tasks, using self.topic."""
        agents = self.agents
        return [
            Task(
                # Use self.topic in description
                description=f"Establish the research objectives and methodology on {self.topic}.",
                expected_output="A detailed research plan.",
                agent=agents["Research Coordinator"]
            ),
            Task(
                # Use self.topic in description
                description=f"Collect relevant data from multiple sources on {self.topic}.",
                expected_output="Compiled research data.",
                agent=agents["Data Researcher"]
            ),
            Task(
                # Use self.topic in description
                description=f"Analyze collected data and identify key insights on {self.topic}.",
                expected_output="Analysis report with major findings.",
                agent=agents["Content Analyst"]
            ),
            Task(
                # Use self.topic in description and expected output
                description=f"Compile findings into a comprehensive report on {self.topic}.",
                expected_output=f"Final research report on {self.topic}.",
                agent=agents["Research Coordinator"]
            )
        ]

    def _create_crew(self):
        """Create and configure the crew."""
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True
        )

    def run(self):
        """Execute the crew's tasks."""
        return self.crew.kickoff()

# Register the HTTP trigger function using the app instance
# Add methods=['POST'] to the route decorator
@app.route(route="ResearchAnalysisCrew", methods=["POST"])
def http_trigger_handler(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("ResearchAnalysisCrew function triggered.")

    topic = None # Initialize topic
    try:
        # Get JSON body
        req_body = req.get_json()
    except ValueError:
        logging.error("Invalid JSON received.")
        return func.HttpResponse(
             "Please pass a valid JSON object in the request body",
             status_code=400
        )

    # Extract topic from JSON
    topic = req_body.get('topic')
    if not topic:
        logging.error("Missing 'topic' in request body.")
        return func.HttpResponse(
             "Please include a 'topic' field in the JSON request body",
             status_code=400
        )

    logging.info(f"Received request for topic: {topic}")

    try:
        # Pass topic to the Crew constructor
        crew = ResearchAnalysisCrew(topic=topic)
        result = crew.run()
        # Include topic in the response
        return func.HttpResponse(f"Final Output for topic '{topic}':\n{result}", status_code=200)
    except Exception as e:
        # Include topic in error logging
        logging.error(f"Error running crew for topic '{topic}': {str(e)}")
        return func.HttpResponse(f"Error processing request for topic '{topic}': {str(e)}", status_code=500)
