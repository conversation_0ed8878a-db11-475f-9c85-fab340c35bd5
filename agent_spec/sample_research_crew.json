{"Name": "Research Analysis Crew", "Description": "A crew of AI agents working together to research, analyze, and summarize topics effectively.", "Agents": [{"Name": "Research Coordinator", "Role": "Research Project Manager", "Goal": ["Coordinate research activities between team members", "Define research objectives and scope", "Review and compile final reports"], "Tools": {"Task Management": "Manage and track research tasks", "Document Review": "Review and provide feedback on documents"}}, {"Name": "Data Researcher", "Role": "Information Gathering Specialist", "Goal": ["Collect relevant data from various sources", "Verify information accuracy", "Organize research findings"], "Tools": {"Web Search": "Search and retrieve information from the internet", "Data Validation": "Verify data accuracy and credibility"}}, {"Name": "Content Analyst", "Role": "Content Analysis Expert", "Goal": ["Analyze collected information", "Identify key patterns and insights", "Create comprehensive summaries"], "Tools": {"Text Analysis": "Analyze text content for key insights", "Summary Generation": "Create concise summaries of findings"}}], "Tasks": [{"Name": "Define Research Scope", "Description": "Establish the research objectives and methodology", "Output": "Detailed research plan", "Agent": "Research Coordinator", "Dependencies": "None"}, {"Name": "Gather Information", "Description": "Collect relevant data from multiple sources", "Output": "Compiled research data", "Dependencies": "Define Research Scope", "Agent": "Data Researcher"}, {"Name": "Analyze Findings", "Description": "Analyze collected data and identify key insights", "Output": "Analysis report", "Dependencies": "Gather Information", "Agent": "Content Analyst"}, {"Name": "Create Final Report", "Description": "Compile findings into a comprehensive report", "Output": "Final research report", "Dependencies": "Analyze Findings", "Agent": "Research Coordinator"}], "Crew": {"Name": "Research Team", "Description": "A collaborative team for comprehensive research and analysis", "Agents": ["Research Coordinator", "Data Researcher", "Content Analyst"], "Tasks": ["Define Research Scope", "Gather Information", "Analyze Findings", "Create Final Report"]}, "Tools": [{"Name": "Task Management", "Description": "Tool for managing and tracking research tasks", "Parameters": {"task_name": "string", "status": "string", "assignee": "string"}}, {"Name": "Web Search", "Description": "Tool for web-based research", "Parameters": {"query": "string", "max_results": "integer"}}, {"Name": "Text Analysis", "Description": "Tool for analyzing text content", "Parameters": {"text": "string", "analysis_type": "string"}}, {"Name": "Document Review", "Description": "Tool for reviewing documents", "Parameters": {"document": "string", "review_criteria": "string"}}, {"Name": "Data Validation", "Description": "Tool for validating data accuracy", "Parameters": {"data": "string", "validation_rules": "string"}}, {"Name": "Summary Generation", "Description": "Tool for generating summaries", "Parameters": {"content": "string", "max_length": "integer"}}]}