import os
import shutil
import logging
from datetime import datetime
from typing import Optional

def backup_function_app_files(src_dir: str, backup_root: str) -> str:
    """
    Copies the contents of src_dir to a versioned backup folder under backup_root.
    Returns the path to the backup folder.
    """
    logger = logging.getLogger(__name__)
    if not os.path.isdir(src_dir):
        logger.error(f"Source directory {src_dir} does not exist for backup.")
        raise FileNotFoundError(f"Source directory {src_dir} does not exist.")
    os.makedirs(backup_root, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = os.path.join(backup_root, timestamp)
    try:
        logger.info(f"Backing up {src_dir} to {backup_dir}")
        shutil.copytree(src_dir, backup_dir)
        logger.info(f"Backup successful: {backup_dir}")
        return backup_dir
    except Exception as e:
        logger.error(f"Backup failed: {e}")
        raise

def rollback_to_previous_backup(backup_root: str, dest_dir: str) -> Optional[str]:
    """
    Finds the most recent backup in backup_root and restores its contents to dest_dir.
    Returns the path to the restored backup, or None if no backup exists.
    """
    logger = logging.getLogger(__name__)
    if not os.path.isdir(backup_root):
        logger.warning(f"Backup root {backup_root} does not exist for rollback.")
        return None
    backups = [d for d in os.listdir(backup_root) if os.path.isdir(os.path.join(backup_root, d))]
    if not backups:
        logger.warning(f"No backups found in {backup_root} for rollback.")
        return None
    backups.sort(reverse=True)  # Most recent first
    latest_backup = os.path.join(backup_root, backups[0])
    try:
        # Remove current dest_dir contents
        if os.path.exists(dest_dir):
            logger.info(f"Removing current contents of {dest_dir} before rollback.")
            shutil.rmtree(dest_dir)
        logger.info(f"Restoring backup from {latest_backup} to {dest_dir}")
        shutil.copytree(latest_backup, dest_dir)
        logger.info(f"Rollback successful: {dest_dir} restored from {latest_backup}")
        return latest_backup
    except Exception as e:
        logger.error(f"Rollback failed: {e}")
        return None 