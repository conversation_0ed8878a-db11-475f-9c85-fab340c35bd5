import os
from typing import Dict, Optional

# Azure SDK imports
try:
    from azure.identity import DefaultAzureCredential
    from azure.mgmt.web import WebSiteManagementClient
except ImportError:
    WebSiteManagementClient = None
    DefaultAzureCredential = None


def get_function_urls_from_azure(
    function_app_name: str,
    resource_group: str,
    subscription_id: str,
    credentials: Optional[object] = None
) -> Dict[str, str]:
    """
    Fetch the list of functions and their invoke URLs from Azure using the SDK/REST API.
    Falls back to environment variables for credentials if not provided.
    Returns a dict mapping function names to their URLs.
    """
    if WebSiteManagementClient is None or DefaultAzureCredential is None:
        raise ImportError("Azure SDK not installed. Please install azure-identity and azure-mgmt-web.")

    # Use provided credentials or fallback to DefaultAzureCredential
    creds = credentials
    if creds is None:
        creds = DefaultAzureCredential()
    # Fallbacks for subscription_id, resource_group, function_app_name
    subscription_id = subscription_id or os.getenv("AZURE_SUBSCRIPTION_ID")
    resource_group = resource_group or os.getenv("AZURE_RESOURCE_GROUP_NAME")
    function_app_name = function_app_name or os.getenv("AZURE_FUNCTION_APP_NAME")
    if not (subscription_id and resource_group and function_app_name):
        raise ValueError("Missing required Azure credentials or identifiers.")

    client = WebSiteManagementClient(creds, subscription_id)
    try:
        functions = client.web_apps.list_functions(resource_group, function_app_name)
        base_url = f"https://{function_app_name}.azurewebsites.net/api"
        url_map = {}
        for func in functions:
            # func.name is in the format 'function_app_name/function_name'
            func_name = func.name.split('/')[-1]
            url_map[func_name] = f"{base_url}/{func_name}"
        return url_map
    except Exception as e:
        raise RuntimeError(f"Failed to fetch function URLs from Azure: {e}")


def construct_function_urls_manually(function_app_name: str, output_dir: str) -> Dict[str, str]:
    """
    Construct function URLs by scanning the output directory for subfolders (function names).
    Returns a dict mapping function names to their URLs.
    """
    base_url = f"https://{function_app_name}.azurewebsites.net/api"
    url_map = {}
    if not os.path.isdir(output_dir):
        raise FileNotFoundError(f"Output directory {output_dir} does not exist.")
    for entry in os.listdir(output_dir):
        full_path = os.path.join(output_dir, entry)
        if os.path.isdir(full_path) and not entry.startswith('.'):
            url_map[entry] = f"{base_url}/{entry}"
    return url_map 