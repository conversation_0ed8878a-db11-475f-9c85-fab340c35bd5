"""
Setup configuration for the Meta Agent package.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="meta_agent",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A tool for generating AI agents",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/meta-agent",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.12",
    install_requires=[
        "crewai>=0.11.0",
        "langchain>=0.1.0",
        "langchain-openai>=0.0.2",
        "openai>=1.3.0",
        "azure-functions>=1.17.0",
        "python-dotenv>=1.0.0",
        "requests>=2.31.0",
        "pyyaml>=6.0.1",
    ],
    entry_points={
        "console_scripts": [
            "meta-agent=meta_agent.generators.crew_ai.cli:main",
        ],
    },
) 