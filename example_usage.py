"""
Example script showing how to create and use the Azure Function.

Before running this script:
1. Make sure you're in the project root directory
2. Install the package in development mode:
   pip install -e .
3. Install Azure Functions Core Tools:
   npm install -g azure-functions-core-tools@4
4. Login to Azure (if deploying):
   az login
"""

import json
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from meta_agent.generators.azure_functions import create_azure_function, deploy_to_azure
from meta_agent.generators.azure_functions.template_manager import AzureFunctionTemplateManager
import requests
import subprocess

def create_sample_spec():
    """Create a sample specification file."""
    spec = {
        "name": "ResearchAnalysisCrew",
        "description": "A crew for conducting research and analysis on specified topics",
        "azure_config": {
            "llm_model": "YOUR_MODEL_NAME",  # e.g., "gpt-4"
            "api_key": "YOUR_API_KEY",
            "base_url": "YOUR_ENDPOINT",
            "function_name": "ResearchAnalysisCrew",
            "auth_level": "function"
        },
        "tools": [
            {
                "name": "Task Management",
                "function_name": "task_management",
                "description": "Manage and track research tasks",
                "parameters": ["task_name: str", "status: str", "assignee: str"],
                "implementation": "return f\"Task '{task_name}' is marked as '{status}' and assigned to '{assignee}'.\""
            },
            {
                "name": "Web Search",
                "function_name": "web_search",
                "description": "Perform web-based research",
                "parameters": ["query: str", "max_results: int = 5"],
                "implementation": "return f\"Performed web search for '{query}' with max {max_results} results.\""
            }
        ],
        "agents": [
            {
                "name": "Research Coordinator",
                "role": "Research Coordinator",
                "goal": "Coordinate research activities and compile reports",
                "backstory": "An expert Research Coordinator with extensive experience",
                "tools": ["task_management"]
            },
            {
                "name": "Data Researcher",
                "role": "Data Researcher",
                "goal": "Collect and verify data from various sources",
                "backstory": "An expert Data Researcher with access to vast information sources",
                "tools": ["web_search"]
            }
        ],
        "tasks": [
            {
                "description": "Establish research objectives and methodology",
                "expected_output": "A detailed research plan",
                "agent": "Research Coordinator"
            },
            {
                "description": "Collect relevant data from multiple sources",
                "expected_output": "Compiled research data",
                "agent": "Data Researcher"
            },
            {
                "description": "Compile findings into a comprehensive report",
                "expected_output": "Final research report",
                "agent": "Research Coordinator"
            }
        ],
        "crew": {
            "process": "sequential",
            "verbose": True,
            "memory": False,
            "cache": True
        }
    }
    
    # Save the spec
    spec_file = project_root / "my_crew_spec.json"
    with open(spec_file, "w") as f:
        json.dump(spec, f, indent=4)
    
    return spec_file

def main():
    # 1. Create base template (only needs to be done once)
    # template_manager = AzureFunctionTemplateManager()
    # if not template_manager.create_base_template():
    #     print("Failed to create base template")
    #     return
        
    # # 2. Create the specification file
    # # spec_file = create_sample_spec()
    # spec_file = project_root / "meta_agent/generators/azure_functions/sample_crew_spec.json"
    # print(f"Created specification file: {spec_file}")
    
    # # 3. Create the Azure Function
    # output_dir = project_root / "my_crew_function"
    
    # Option 1: Create locally only
    # success = create_azure_function(
    #     spec_file=str(spec_file),
    #     output_dir=str(output_dir)
    # )
    
    # Option 2: Create and deploy to Azure
    # success = create_azure_function(
    #     spec_file=str(spec_file),
    #     output_dir=str(output_dir),
    #     deploy=True,
    #     function_app_name="crewTest",
    #     resource_group="bincha-test-1_group"
    # )

    output_dir = '/Users/<USER>/Documents/Trail/meta-agent/my_crew_function'    

    # Option 3: deploy to azure
    success = deploy_to_azure(
        output_dir=str(output_dir),
        function_app_name="TestingCrew",
        resource_group="bincha-test-1_group"
    )
    
    if not success:
        print("Failed to create Azure Function")
        return
    
    print(f"Created Azure Function in: {output_dir}")
    
    # 4. Test locally
    try:
        # Navigate to function directory
        os.chdir(output_dir)
        
        # Start the function
        print("Starting Azure Function...")
        func_process = subprocess.Popen(["func", "start"])
        print("Function started. Press Ctrl+C to stop.")
        
        # Test the function
        input("Press Enter to test the function...")
        
        # Send a test request
        response = requests.post(
            "http://localhost:7071/api/MarketResearchCrew",
            json={
                "topic": "artificial intelligence",
                "word_count": 100
            }
        )
        
        print("\nResponse:", response.status_code)
        print(response.text)
        
        # Wait for user to stop
        input("\nPress Enter to stop the function...")
        
    finally:
        # Clean up
        if 'func_process' in locals():
            func_process.terminate()
            func_process.wait()
        
if __name__ == "__main__":
    main() 