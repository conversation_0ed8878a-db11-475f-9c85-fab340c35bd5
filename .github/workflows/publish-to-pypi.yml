name: Publish to PyPI

on:
  workflow_run:
    workflows: ["Python Tests"]
    branches: [main]
    types:
      - completed
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'release' }}
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
        ref: main
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        
    - name: Install dependencies
      run: |
        poetry install --without dev
    
    - name: Check package version
      run: |
        echo "Package version according to Poetry:"
        poetry version
        
    - name: Build package
      run: |
        poetry build
        echo "Built package files:"
        ls -la dist/
      
    - name: Publish to PyPI
      run: |
        poetry config pypi-token.pypi ${{ secrets.PYPI_API_TOKEN }}
        poetry publish --no-interaction --skip-existing
