name: Run Python Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Specify Python versions to test against

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4 # Use latest version

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5 # Use latest version
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1 # Use a dedicated action for Poetry installation
      with:
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Install dependencies
      run: poetry install --with dev # Install dev dependencies for testing

    - name: Run tests with pytest
      run: poetry run pytest # Use poetry run to execute pytest in the virtual environment
